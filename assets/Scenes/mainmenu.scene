[{"__type__": "cc.SceneAsset", "_name": "mainmenu", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "mainmenu", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 114}, {"__id__": 116}, {"__id__": 118}], "_active": true, "_components": [], "_prefab": {"__id__": 132}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": true, "_globals": {"__id__": 133}, "_id": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 8}, {"__id__": 11}, {"__id__": 18}, {"__id__": 25}, {"__id__": 32}, {"__id__": 37}, {"__id__": 40}, {"__id__": 64}, {"__id__": 81}, {"__id__": 94}], "_active": true, "_components": [{"__id__": 111}, {"__id__": 112}, {"__id__": 113}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 640, "y": 360.00000000000006, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 423.7241379310345, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}, {"__id__": 7}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4fvExLMftACaMzehymgVZR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1650.8790000000001, "height": 927.8346875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7ciWOEsshG8o1VYfW9qqXo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "de9038ee-21fe-4f18-9b9c-ac2644ea9723@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "61bt3qIF1KI6kTJNySf7CS"}, {"__type__": "cc.Node", "_name": "logo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 10}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -22.533000000000015, "y": 181.15499999999992, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "23DJODkolCzaa74pLNc8iJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 488.76500000000004, "height": 357.68999999999994}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c1rUntTrVAlKjIvPMW3ZnK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dddb3d97-5b9e-4cc8-acdf-f9e4453ac948@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "eaA4+mgzxOBKGI30DvMagV"}, {"__type__": "cc.Node", "_name": "startButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}, {"__id__": 17}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.9, "y": -80.462, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2fZbf5v6lInIBcpbbr1WL/"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1daj+Gr4hLxoZEcy8bs4fZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 219.0244140625, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b4pG2ALlxN+pyQTDNeNaSg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 202, "g": 61, "b": 61, "a": 255}, "_string": "单人游戏 single", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "b2rOnuPr9I3Zb8zcHpFRMF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 248.865, "height": 71.20889379286326}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "87qmaadZBL759Lnx83taVP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 243, "g": 233, "b": 233, "a": 255}, "_spriteFrame": {"__uuid__": "4cf1948a-ffca-426e-9c43-1e7c864a1b60@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7a6PAdVRxPFrOSYBQOpb+F"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "4cf1948a-ffca-426e-9c43-1e7c864a1b60@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 11}, "_id": "dd7TPo4OJCTIzTeq4PQdWb"}, {"__type__": "cc.Node", "_name": "resetButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 19}], "_active": true, "_components": [{"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 2.472, "y": -164.535, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "335rG0iKNOx4uK6rtqlNFs"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a2TBYXvVZHCr6nP/qrA1hF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 204.0537109375, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "52oHvLti9KroTNfOt/PA86"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 238, "g": 220, "b": 89, "a": 255}, "_string": "重制进度 reset", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 12, "g": 11, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 27, "g": 29, "b": 3, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "64590eJ65DdYJR1Yiu/7nG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 257.85, "height": 73.77981341084441}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "de+UJOPohO1ZoFP6qgu7B8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 243, "g": 233, "b": 233, "a": 255}, "_spriteFrame": {"__uuid__": "593904f2-fa85-498f-9b0e-9f79890c24fd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "eboVZ7alZA9I9LL6nvSh3B"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "593904f2-fa85-498f-9b0e-9f79890c24fd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 18}, "_id": "66SYDNsq1Jc5LuOlIQ6PgD"}, {"__type__": "cc.Node", "_name": "helpButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 26}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 2.472, "y": -254.486, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a5jfJFn3BARL+KF6viBNR5"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [{"__id__": 27}, {"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9c5q/jEZtHQK2fszoqsw/E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 194.0048828125, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "61KShu+zZNLIoH8K/LuOkg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 86, "g": 223, "b": 157, "a": 255}, "_string": "游戏说明 help", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 10, "g": 22, "b": 6, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "33DFlhjrVFob9xntqlNrKN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 257.85, "height": 73.77981341084441}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "55o2FZeMNAPqN0NQZ5vldG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 243, "g": 233, "b": 233, "a": 255}, "_spriteFrame": {"__uuid__": "1c18f6e7-3b13-401b-85a1-746bf7284949@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ec6g7OZadJg73U4NP2lkQ7"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "1c18f6e7-3b13-401b-85a1-746bf7284949@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 25}, "_id": "2evfdbJU1JBbxbpt31+IyJ"}, {"__type__": "cc.Node", "_name": "settingButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 550.181, "y": 294.047, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "90ZiYpiOhE2pNkntPvIxWI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 66.17800000000001, "height": 67.19247971371924}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6GQOSdLFJ9Y+iA6noZiJI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4b2a90e7-9654-43f7-b19c-1929bfdc1fa9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b8vxjt7JtPE5OvaJ9DYXRS"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 32}, "_id": "5e6sBxYRNPYpG7O+HC1zKB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_alignFlags": 33, "_target": {"__id__": 2}, "_left": 0, "_right": 0.04432031249999989, "_top": 0.044939944643250504, "_bottom": 335, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 50, "_alignMode": 1, "_lockFlags": 0, "_id": "86q8MljrdID7GZrcVI1EmA"}, {"__type__": "cc.Node", "_name": "traffic", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 38}, {"__id__": 39}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -190.725, "y": -174.653, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "92EVW5NXlA96UU0Nm5zZw1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 124.47400000000002, "height": 298.7376}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ffK48U+cRPE6kuMcON3S22"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "83baeef7-746b-4f91-9a84-fd8a58d2b8b3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "56ka+pY+VBB77HGkAEttzJ"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 41}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "81520cf5-3a02-463b-b3ec-c8870a3f4940", "__expectedType__": "cc.Prefab"}, "fileId": "98m1scCCpHFaAD50RjkFIZ", "instance": {"__id__": 42}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3bBm5N8+NLeYdVfjOnRTcE", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 43}, {"__id__": 45}, {"__id__": 47}, {"__id__": 49}, {"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}, {"__id__": 61}, {"__id__": 63}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_name"], "value": "loading"}, {"__type__": "cc.TargetInfo", "localID": ["98m1scCCpHFaAD50RjkFIZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -3.42999999999995, "y": 1.3064999999999714, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["98m1scCCpHFaAD50RjkFIZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 48}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["98m1scCCpHFaAD50RjkFIZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 50}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["98m1scCCpHFaAD50RjkFIZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 1675.126, "height": 936.391}}, {"__type__": "cc.TargetInfo", "localID": ["dftExsNEZHn4dtPomyMxdT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 54}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "de9038ee-21fe-4f18-9b9c-ac2644ea9723@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["50p6T0zeZHzqrT8IjFBzum"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 187, "g": 177, "b": 177, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["50p6T0zeZHzqrT8IjFBzum"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["50p6T0zeZHzqrT8IjFBzum"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_type"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["50p6T0zeZHzqrT8IjFBzum"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 62}, "propertyPath": ["_isTrimmedMode"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["50p6T0zeZHzqrT8IjFBzum"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 50}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_name": "SettingPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 65}, {"__id__": 68}, {"__id__": 72}], "_active": false, "_components": [{"__id__": 79}, {"__id__": 80}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.8537647541830893, "y": -30.046650789554917, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b984owl+5NMpv54c8KdN9M"}, {"__type__": "cc.Node", "_name": "soundtext", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 127.337, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d0gEVm1XJHTbQyi/423ubX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 390.509033203125, "height": 94.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "34KYFtALpGs7sY+xsN4RRE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 219, "g": 130, "b": 134, "a": 255}, "_string": "打开或关闭声音\nturn on / turn off the sound effect", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24.5, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2bsrQjLwlMvoITby+ktMHB"}, {"__type__": "cc.Node", "_name": "close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 70}, {"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 212.483, "y": 235.404, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2cS6R9VhFGlqzkoCxg9K5A"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 55.50500000000002, "height": 55.50500000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7bI/+r6TFCRpbPtNjxibI3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a426c2c8-58b1-4e5a-a16c-e75061a2d034@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f4PY5V37NDIJ+Z7pND2xam"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "e6AfEVBy5GB7MP1GAJ/MhP"}, {"__type__": "cc.Node", "_name": "audioButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 64}, "_children": [{"__id__": 73}], "_active": true, "_components": [{"__id__": 76}, {"__id__": 77}, {"__id__": 78}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -179.864, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0eBvcHSOxGUI6QAfljrBrt"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13E1crnQlEA7zXIhS+XsC6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 91.19140625, "height": 56.047999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2buzICbMpN86XtkAqevkF7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 158, "g": 98, "b": 42, "a": 255}, "_string": "音效:开\nSound: on", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24.8, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "4eaZF6qTNBPZK5I49i10xn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280.995, "height": 81.87200000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b9VUQVm3ZPEaTKJ14AcxHI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4cf1948a-ffca-426e-9c43-1e7c864a1b60@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "49oaiyANREVr+jEV0cV06d"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "7cimAjY5ZJ24XXoXKRkN2/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 509, "height": 558}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "87EQaouDdDuIYPFb4QDZ+1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a0179db-b0bc-4905-8953-8ab076a3cfd6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "deg+AbLZ9EfbywnADch9jv"}, {"__type__": "cc.Node", "_name": "HelpPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 82}, {"__id__": 86}, {"__id__": 89}], "_active": false, "_components": [{"__id__": 92}, {"__id__": 93}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 21.627, "y": 0.529, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c3CpLDMQBIx4e/4jmuAoTg"}, {"__type__": "cc.Node", "_name": "close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}, {"__id__": 85}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 367.486, "y": 300.922, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ddWQE/uDpHC5JNho1cGczy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 55.50500000000002, "height": 55.50500000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a42VttFMRHI7P5ZsCAJ2qS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a426c2c8-58b1-4e5a-a16c-e75061a2d034@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2aQTeuMU9Ep6bRu81ohw2L"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "de06GviLpOqp8v/35yeTFO"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -7.99, "y": 302.016, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "42q9Dm3whIqKsP1QLpNDzO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 188.173583984375, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "63muEUY+BHmLgBsIdhgtHl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 122, "g": 218, "b": 130, "a": 255}, "_string": "help/帮助", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 42.5, "_fontSize": 42.5, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2aTLYWM1lP97bDowtNIFKQ"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 5.428, "y": -21.068, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebYLPfkpFK55jFDrtEt9TB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 779.6208437500001, "height": 508.804}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "25Fyr6IIBMvJicJR1XUnmd"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 212, "g": 167, "b": 218, "a": 255}, "_string": "不同的车辆拥有不同特性和武器，用出色的驾驶和甩尾技术在有限的时间内喷洒更多颜料！\n必要时，可使用车辆的专属武器攻击对手/移除对手已喷洒的燃料\n倒计时结束后，依据玩家颜料占比进行评分\nS:45%  A:35%  B:25%  F:<25%\n\nDifferent cars have different features and weapons. \nUse your excellent driving and drifting skills \nto spray more paint within the limited time! \nWhen necessary, use the exclusive weapon to attack your opponents or remove the fuel they have sprayed.\nAfter the countdown ends, players will be scored based on their proportion of paint. \nS:45%  A:35%  B:25%  F:<25%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.6, "_fontSize": 25.6, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35.4, "_overflow": 3, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "b7yBVRlYFOqaYkQ+h6HZyY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 850.965, "height": 702.41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9ddziMU0NBx5T+K8TfCM7W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a0179db-b0bc-4905-8953-8ab076a3cfd6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "eb/XzCEelL8byVvSfhb3l8"}, {"__type__": "cc.Node", "_name": "ResetPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 95}, {"__id__": 99}, {"__id__": 106}], "_active": false, "_components": [{"__id__": 109}, {"__id__": 110}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.8537647541830893, "y": -30.046650789554917, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e7Hqe8WFdMlLIFiRv9MqJ0"}, {"__type__": "cc.Node", "_name": "close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}, {"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 212.483, "y": 235.404, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "11Pd5JC7VBSIhrAWl/4Mn5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 55.50500000000002, "height": 55.50500000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eerf2E7xpOWo3lVpqUzvfn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a426c2c8-58b1-4e5a-a16c-e75061a2d034@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "00/Rt+NQpOCJlPU566lF48"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "67zS9EpktHDYAdWR5IrFYV"}, {"__type__": "cc.Node", "_name": "confirmButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [{"__id__": 100}], "_active": true, "_components": [{"__id__": 103}, {"__id__": 104}, {"__id__": 105}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -204.353, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dejK5MDjhDa5sSwITKcDcS"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [{"__id__": 101}, {"__id__": 102}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1axj+bnHNHfLeMT2q/RUpY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 65.56640625, "height": 56.047999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bacAm20vNEgbxLlgL8C7aJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 158, "g": 98, "b": 42, "a": 255}, "_string": "确认\nconfirm", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24.8, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "fexylAKKNExLCDdWtuD/KD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280.995, "height": 81.87200000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "55LTYqzWBIRqKA/D9lI5oZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "593904f2-fa85-498f-9b0e-9f79890c24fd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6d51F/6idJ64df5D02uhUO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "06sQuDJHZH2JwNtyTmEO+V"}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 139.085, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "34u26pfXZPKp9TdcCbqfsA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 409.6884765625, "height": 94.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4eImWIRu1BfI5D2hdYRe/7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 238, "g": 106, "b": 73, "a": 255}, "_string": "确认要重制进度吗？\ncomfirm to reset player progress?", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "f03LF7HEtFNJf6gT4ndTU3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 509, "height": 558}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "78hEJ3iORGcaCF6hgeBNLG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a0179db-b0bc-4905-8953-8ab076a3cfd6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2eREKxhSVKGZ6T27Dk+cYP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 115}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ef0ZsUXShJ9pBKMQh7xWv7"}, {"__type__": "1b6d6aHKXNGGK9721LvZ7jB", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": null, "_id": "c4OSmYCAZG6KDBR7O5f8pO"}, {"__type__": "cc.Node", "_name": "MainMenuController", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 117}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1eIGv73uxJ149Ou4e8BJTt"}, {"__type__": "0cf64BckYpA8bODaKn8c5t/", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "startGameBtn": {"__id__": 17}, "settingBtn": {"__id__": 35}, "closesettingBtn": {"__id__": 71}, "audioBtn": {"__id__": 78}, "settingPanel": {"__id__": 64}, "audioLabel": {"__id__": 75}, "helpButton": {"__id__": 31}, "closehelpBtn": {"__id__": 85}, "helpPanel": {"__id__": 81}, "resetProgressBtn": {"__id__": 24}, "resetProgressConfirmPanel": {"__id__": 94}, "confirmResetBtn": {"__id__": 105}, "closeResetPanelBtn": {"__id__": 98}, "_id": "cc6u19Dg9J8b8bavBoTJEx"}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 119}, {"__id__": 126}, {"__id__": 127}, {"__id__": 125}, {"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 123}, {"__id__": 124}, {"__id__": 128}, {"__id__": 129}, {"__id__": 130}, {"__id__": 131}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f8zc0C52RFyJSZjqngqV7J"}, {"__type__": "7c3850Yg29IBK4wW9FYz7f1", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "bgmAudioSource": {"__id__": 120}, "bgmbattleAudioSource": {"__id__": 121}, "bgmbattle2AudioSource": {"__id__": 122}, "buttonClickAudioSource": {"__id__": 123}, "carCollisionAudioSource": {"__id__": 124}, "carDestructionAudioSource": {"__id__": 125}, "carStartAudioSource": {"__id__": 126}, "carDriftAudioSource": {"__id__": 127}, "weaponFireAudioSource": {"__id__": 128}, "rocketHitAudioSource": null, "bulletHitAudioSource": null, "dartHitAudioSource": null, "_id": "bfCgYEP+xJia6gXNUl6mC3"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "556a0967-2aed-4c42-b221-0a202d1954ff", "__expectedType__": "cc.AudioClip"}, "_loop": true, "_playOnAwake": false, "_volume": 1, "_id": "f9E3KRwedANrBQ0dHhYQKd"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "f0126de9-908b-4055-a7c6-b0802b6ba2f3", "__expectedType__": "cc.AudioClip"}, "_loop": true, "_playOnAwake": false, "_volume": 1, "_id": "5ecm+pRvBI9JNz+45L+JHR"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "0bc8a78b-bd7d-4fe0-82d4-c54b64b885bf", "__expectedType__": "cc.AudioClip"}, "_loop": true, "_playOnAwake": false, "_volume": 1, "_id": "8aPKI7EqZAT4PwX1hI1bwT"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "2f600d94-1f16-4ec5-aaeb-c6da789fe738", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "51F2aGDOpHMKqdqZW0vSPi"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "71048de6-acd5-49cc-985f-60f651aa9064", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "46AGracOJBrLeYmPGvtSv+"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "efd1babe-05af-45cc-bec4-21dbf23b0ff0", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "773IfuLSZBGKV4wjU2Ucbx"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "93a6c42e-99bb-43fc-929d-600829e7c672", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "86AvN+tRxKs5hD62ffnnZh"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "bd7fc9e7-382a-4306-bfb0-ed0ed18cc8bb", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "43LryMWipPbJMKho8MI0kz"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "f0a18dd3-9aab-4386-b491-e05d74746be3", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "20D3aRzlBPNLfpvgip1f9y"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "42ed8357-8bef-4b77-8c7b-883d8d0f7bee", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "70A9nIG09Ak7Agz46t/A8u"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "6a4569e1-e7a8-4813-bd13-eba6d16a217f", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "64su5e0ApNlYfn9f3INNfj"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "6060291b-d1ad-4bfd-95a7-384f32005584", "__expectedType__": "cc.AudioClip"}, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "c2rpZERKJPXIn5XlRINbEz"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 40}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 134}, "shadows": {"__id__": 135}, "_skybox": {"__id__": 136}, "fog": {"__id__": 137}, "octree": {"__id__": 138}, "skin": {"__id__": 139}, "lightProbeInfo": {"__id__": 140}, "postSettings": {"__id__": 141}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]