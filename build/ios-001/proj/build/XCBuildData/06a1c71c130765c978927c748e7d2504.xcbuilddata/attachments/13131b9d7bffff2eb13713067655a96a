-target arm64-apple-ios18.2 -fpascal-strings -O0 '-DCMAKE_INTDIR="Debug-iphoneos"' -DBOOST_CONTAINER_NO_LIB -DBOOST_CONTAINER_STATIC_LINK -DBOOST_UUID_FORCE_AUTO_LINK '-DCC_PLATFORM_WINDOWS=2' '-DCC_PLATFORM_MACOS=4' '-DCC_PLATFORM_IOS=1' '-DCC_PLATFORM_MAC_OSX=4' '-DCC_PLATFORM_MAC_IOS=1' '-DCC_PLATFORM_ANDROID=3' '-DCC_PLATFORM_OHOS=5' '-DCC_PLATFORM_LINUX=6' '-DCC_PLATFORM_QNX=7' '-DCC_PLATFORM_NX=8' '-DCC_PLATFORM_OPENHARMONY=10' '-DCC_PLATFORM_EMSCRIPTEN=9' '-DCC_PLATFORM=1' -DBOOST_NO_CXX98_FUNCTION_BASE '-DCC_NETMODE_CLIENT=0' '-DCC_NETMODE_LISTEN_SERVER=1' '-DCC_NETMODE_HOST_SERVER=2' '-DCC_NETMODE=0' '-DBOOST_ALL_NO_LIB=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/boost/container/archives/Debug/include -I/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/boost/container/喷射飞车-SuperSplash.build/Debug-iphoneos/boost_container.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/boost/container/喷射飞车-SuperSplash.build/Debug-iphoneos/boost_container.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/boost/container/喷射飞车-SuperSplash.build/Debug-iphoneos/boost_container.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/boost/container/archives/Debug '-std=gnu99'