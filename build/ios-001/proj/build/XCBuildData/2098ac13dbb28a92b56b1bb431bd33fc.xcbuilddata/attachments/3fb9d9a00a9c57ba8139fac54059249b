#!/bin/sh
set -e
if test "$CONFIGURATION" = "Debug"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /opt/homebrew/Cellar/node/24.5.0/bin/node /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi
if test "$CONFIGURATION" = "Release"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /opt/homebrew/Cellar/node/24.5.0/bin/node /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi
if test "$CONFIGURATION" = "MinSizeRel"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /opt/homebrew/Cellar/node/24.5.0/bin/node /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi
if test "$CONFIGURATION" = "RelWithDebInfo"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /opt/homebrew/Cellar/node/24.5.0/bin/node /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi

