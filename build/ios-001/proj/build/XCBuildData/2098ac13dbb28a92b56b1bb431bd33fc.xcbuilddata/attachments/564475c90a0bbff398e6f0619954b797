#!/bin/sh
set -e
if test "$CONFIGURATION" = "Debug"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
fi
if test "$CONFIGURATION" = "Release"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
fi
if test "$CONFIGURATION" = "MinSizeRel"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
fi
if test "$CONFIGURATION" = "RelWithDebInfo"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj
fi

