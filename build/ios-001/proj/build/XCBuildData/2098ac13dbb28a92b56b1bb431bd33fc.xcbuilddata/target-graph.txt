Target dependency graph (7 targets)
Target 'builtin-res' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'ZERO_CHECK' in project '喷射飞车-SuperSplash'
Target 'ALL_BUILD' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'ZERO_CHECK' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'cocos_engine' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'SuperSplash' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'boost_container' in project '喷射飞车-SuperSplash'
Target 'SuperSplash' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'ZERO_CHECK' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'cocos_engine' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'boost_container' in project '喷射飞车-SuperSplash'
Target 'cocos_engine' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'ZERO_CHECK' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'genbindings' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'boost_container' in project '喷射飞车-SuperSplash'
Target 'boost_container' in project '喷射飞车-SuperSplash' (no dependencies)
Target 'genbindings' in project '喷射飞车-SuperSplash'
➜ Explicit dependency on target 'ZERO_CHECK' in project '喷射飞车-SuperSplash'
Target 'ZERO_CHECK' in project '喷射飞车-SuperSplash' (no dependencies)