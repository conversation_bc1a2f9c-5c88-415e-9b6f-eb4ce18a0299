{"buildCommand": {"command": "prepareForIndexing", "enableIndexBuildArena": false, "targets": null}, "configuredTargets": [{"guid": "c548deb85243196e1c4a52680c1a091a973013dc8993fc96166375f163af9ebe"}], "containerPath": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/喷射飞车-SuperSplash.xcodeproj", "continueBuildingAfterErrors": true, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphoneos", "sdk": "iphoneos18.2", "sdkVariant": "iphoneos", "supportedArchitectures": ["arm64e", "arm64v8", "arm64", "armv8"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/喷射飞车-SuperSplash-bcgrjkbvgidkdcafwrtzlunqveus/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/喷射飞车-SuperSplash-bcgrjkbvgidkdcafwrtzlunqveus/Index.noindex/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Debug", "overrides": {"synthesized": {"table": {"ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone14,7", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "26.0", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone14,7", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "TARGET_DEVICE_IDENTIFIER": "00008110-000A59040151401E", "TARGET_DEVICE_MODEL": "iPhone14,7", "TARGET_DEVICE_OS_VERSION": "26.0", "TARGET_DEVICE_PLATFORM_NAME": "iphoneos"}}}}, "qos": "default", "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": true, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}