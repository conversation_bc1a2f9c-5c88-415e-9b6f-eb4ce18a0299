[1, ["4c8ZSK/8pCbpxDHnyGShtg@f9941", "59OQTy+oVJj5sOn3mJDCT9@f9941", "a4JsLIWLFOWqFs51BhotA0@f9941", "6aAXnbsLxJBYlTirB2o8/W@f9941", "dekDjuIf5PGJucrCZE6pcj@f9941", "1cGPbnOxNAG4WhdGv3KElJ@f9941", "1cGPbnOxNAG4WhdGv3KElJ@6c48a", "4bKpDnllRD97GcGSm/3B+p@6c48a", "83uu73dGtPkZqE/YpY0riz@6c48a", "dd2z2XW55MyKzf+eRFOslI@f9941", "4bKpDnllRD97GcGSm/3B+p@f9941", "83uu73dGtPkZqE/YpY0riz@f9941", "81Ugz1OgJGO7PsyIcKP0lA", "427YNXi+9Ld4x7iD2ND3vu", "6aRWnh56hIE70T66bRaiF/", "60YCkb0a1L/ZWnOE8yAFWE", "55aglnKu1MQrIhCiAtGVT/", "f0Em3pkItAVafGsIAra6Lz", "0byKeLvX1P4ILUxUtkuIW/", "2fYA2UHxZOxarrxtp4n+c4", "71BI3mrNVJzJhfYPZRqpBk", "ef0bq+Ba9FzL7EIdvyOw/w", "93psQumbtD/JKdYAgp58Zy", "bdf8nnOCpDBr+w7Q7RjMi7", "f0oY3TmqtDhrSR4F10dGvj", "dd2z2XW55MyKzf+eRFOslI@6c48a", "dekDjuIf5PGJucrCZE6pcj@6c48a"], ["node", "_spriteFrame", "_clip", "_textureSource", "_normalSprite", "targetInfo", "_parent", "_cameraComponent", "weaponFireAudioSource", "carDriftAudioSource", "carStartAudioSource", "carDestructionAudioSource", "carCollisionAudioSource", "buttonClickAudioSource", "bgmbattle2AudioSource", "bgmbattleAudioSource", "bgmAudioSource", "root", "scene", "asset", "value"], [["cc.Node", ["_name", "_layer", "_id", "_obj<PERSON><PERSON>s", "_active", "__editorExtras__", "_components", "_parent", "_lpos", "_children", "_prefab"], -3, 9, 1, 5, 2, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "_enableOutline", "_enableShadow", "_lineHeight", "_enableWrapText", "_overflow", "node", "_color", "_shadowColor", "_outlineColor"], -6, 1, 5, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_spriteFrame", "_color"], 0, 1, 6, 5], ["cc.Node", ["_name", "_layer", "_id", "_components", "_parent", "_lpos", "_children"], 0, 12, 1, 5, 2], ["cc.AudioSource", ["_playOnAwake", "_loop", "node", "_clip"], 1, 1, 6], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_alignMode", "_right", "_isAbsRight", "_isAbsTop", "_originalHeight", "node", "_target"], -5, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "_normalColor", "_target"], 2, 1, 5, 1], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.UITransform", ["node", "_contentSize"], 3, 1, 5], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["7c3850Yg29IBK4wW9FYz7f1", ["node", "bgmAudioSource", "bgmbattleAudioSource", "bgmbattle2AudioSource", "buttonClickAudioSource", "carCollisionAudioSource", "carDestructionAudioSource", "carStartAudioSource", "carDriftAudioSource", "weaponFireAudioSource"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Scene", ["_name", "autoReleaseAssets", "_children", "_prefab", "_globals"], 1, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -2, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["cc.TargetInfo", ["localID"], 2], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_visibility", "node", "_color"], -1, 1, 5], ["1b6d6aHKXNGGK9721LvZ7jB", ["node"], 3, 1], ["0cf64BckYpA8bODaKn8c5t/", ["node", "startGameBtn", "settingBtn", "closesettingBtn", "audioBtn", "settingPanel", "audioLabel", "helpButton", "closehelpBtn", "helpPanel", "resetProgressBtn", "resetProgressConfirmPanel", "confirmResetBtn", "closeResetPanelBtn"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[10, 0, 1, 1], [31, 0, 2], [3, 0, 3, 4, 2], [0, 0, 1, 7, 6, 8, 3], [5, 0, 2, 2], [4, 0, 1, 4, 6, 3, 5, 3], [7, 0, 1, 2], [4, 0, 1, 4, 3, 5, 3], [26, 0, 1, 2, 3], [27, 0, 1, 2, 2], [7, 0, 1, 2, 3, 2], [0, 0, 4, 1, 7, 9, 6, 8, 4], [0, 0, 3, 1, 7, 6, 4], [5, 0, 2, 3, 2], [5, 1, 0, 2, 3], [3, 1, 0, 3, 5, 4, 3], [0, 0, 1, 7, 6, 3], [0, 0, 2, 7, 6, 3], [3, 3, 4, 1], [1, 0, 1, 2, 3, 4, 5, 9, 10, 7], [1, 0, 1, 2, 6, 9, 10, 5], [8, 0, 2], [0, 0, 1, 2, 9, 6, 8, 4], [0, 3, 5, 7, 10, 3], [4, 0, 2, 3, 3], [4, 0, 1, 4, 3, 3], [9, 0, 1, 2, 3, 2], [11, 0, 1, 1], [6, 0, 1, 2, 3, 8, 5], [6, 0, 4, 1, 2, 5, 6, 7, 3, 8, 9, 9], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [3, 1, 0, 3, 4, 3], [3, 1, 0, 2, 3, 4, 4], [13, 0, 1, 2, 3, 4, 3], [14, 0, 1, 2, 3, 4, 5, 6], [15, 0, 1, 2, 3, 4, 5, 4], [16, 0, 1, 2, 3, 4, 5, 6, 7, 1], [17, 0, 1, 1], [18, 0, 1, 1], [19, 1], [20, 1], [21, 1], [22, 0, 2], [23, 1], [24, 1], [1, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 9], [1, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 12, 11, 9], [1, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 11, 9], [1, 0, 1, 2, 8, 3, 4, 5, 9, 10, 8], [1, 0, 1, 2, 6, 8, 3, 4, 5, 9, 10, 9], [25, 0, 1, 2, 3], [28, 0, 1, 2, 2], [29, 0, 1, 2, 2], [30, 0, 1, 2, 3], [32, 0, 1, 2, 3, 4, 5, 5], [33, 0, 1], [34, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1]], [[[{"name": "green", "rect": {"x": 0, "y": 0, "width": 270, "height": 206}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 270, "height": 206}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-135, -103, 0, 135, -103, 0, -135, 103, 0, 135, 103, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 206, 270, 206, 0, 0, 270, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -135, "y": -103, "z": 0}, "maxPos": {"x": 135, "y": 103, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [6]], [[{"name": "setting", "rect": {"x": 0, "y": 0, "width": 1067, "height": 1054}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1067, "height": 1054}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-533.5, -527, 0, 533.5, -527, 0, -533.5, 527, 0, 533.5, 527, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1054, 1067, 1054, 0, 0, 1067, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -533.5, "y": -527, "z": 0}, "maxPos": {"x": 533.5, "y": 527, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [7]], [[{"name": "traffic", "rect": {"x": 0, "y": 0, "width": 640, "height": 1536}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 640, "height": 1536}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-320, -768, 0, 320, -768, 0, -320, 768, 0, 320, 768, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1536, 640, 1536, 0, 0, 640, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -320, "y": -768, "z": 0}, "maxPos": {"x": 320, "y": 768, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [8]], [[[21, "mainmenu"], [22, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16], [[0, -1, [5, 1280, 720]], [27, -3, -2], [28, 45, 5.684341886080802e-14, 5.684341886080802e-14, 1, -4]], [1, 640, 360.00000000000006, 0]], [24, "SoundManager", "f8zc0C52RFyJSZjqngqV7J", [[[30, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17], -27, -28, -29, -30, -31, -32, -33, -34, -35, [13, false, -36, 17], [13, false, -37, 18], [13, false, -38, 19]], 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 4, 4]], [11, "SettingPanel", false, 33554432, 1, [-41, -42, -43], [[0, -39, [5, 509, 558]], [18, -40, 11]], [1, 0.8537647541830893, -30.046650789554917, 0]], [11, "HelpPanel", false, 33554432, 1, [-46, -47, -48], [[0, -44, [5, 850.965, 702.41]], [2, 0, -45, 13]], [1, 21.627, 0.529, 0]], [11, "ResetPanel", false, 33554432, 1, [-51, -52, -53], [[0, -49, [5, 509, 558]], [18, -50, 16]], [1, 0.8537647541830893, -30.046650789554917, 0]], [5, "startButton", 33554432, 1, [-57], [[[0, -54, [5, 248.865, 71.20889379286326]], [15, 1, 0, -55, [4, 4293519859], 2], -56], 4, 4, 1], [1, 0.9, -80.462, 0]], [5, "resetButton", 33554432, 1, [-61], [[[0, -58, [5, 257.85, 73.77981341084441]], [15, 1, 0, -59, [4, 4293519859], 3], -60], 4, 4, 1], [1, 2.472, -164.535, 0]], [5, "helpButton", 33554432, 1, [-65], [[[0, -62, [5, 257.85, 73.77981341084441]], [15, 1, 0, -63, [4, 4293519859], 4], -64], 4, 4, 1], [1, 2.472, -254.486, 0]], [7, "settingButton", 33554432, 1, [[[0, -66, [5, 66.17800000000001, 67.19247971371924]], [31, 1, 0, -67, 5], -68, [29, 33, 0.04432031249999989, 0.044939944643250504, 335, false, false, 50, 1, -69, 1]], 4, 4, 1, 4], [1, 550.181, 294.047, 0]], [33, "mainmenu", true, [1, -71, -72, 2], [34, null, null, "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", null, null, [-70]], [36, [37, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [38, [4, 4283190348], [0, 512, 512]], [39], [40], [41], [42, false], [43], [44]]], [5, "audioButton", 33554432, 3, [-76], [[[0, -73, [5, 280.995, 81.87200000000001]], [2, 0, -74, 10], -75], 4, 4, 1], [1, 0, -179.864, 0]], [5, "confirmButton", 33554432, 5, [-80], [[[0, -77, [5, 280.995, 81.87200000000001]], [2, 0, -78, 15], -79], 4, 4, 1], [1, 0, -204.353, 0]], [7, "close", 33554432, 3, [[[0, -81, [5, 55.50500000000002, 55.50500000000002]], [2, 0, -82, 9], -83], 4, 4, 1], [1, 212.483, 235.404, 0]], [7, "close", 33554432, 4, [[[0, -84, [5, 55.50500000000002, 55.50500000000002]], [2, 0, -85, 12], -86], 4, 4, 1], [1, 367.486, 300.922, 0]], [7, "close", 33554432, 5, [[[0, -87, [5, 55.50500000000002, 55.50500000000002]], [2, 0, -88, 14], -89], 4, 4, 1], [1, 212.483, 235.404, 0]], [16, "background", 33554432, 1, [[0, -90, [5, 1650.8790000000001, 927.8346875]], [32, 1, 0, false, -91, 0]]], [3, "logo", 33554432, 1, [[0, -92, [5, 488.76500000000004, 357.68999999999994]], [2, 0, -93, 1]], [1, -22.533000000000015, 181.15499999999992, 0]], [12, "Label", 512, 33554432, 6, [[0, -94, [5, 219.0244140625, 41.8]], [45, "单人游戏 single", 30, 30, 30, false, true, true, true, -95, [4, 4282203594]]]], [12, "Label", 512, 33554432, 7, [[0, -96, [5, 204.0537109375, 41.8]], [46, "重制进度 reset", 30, 30, 30, false, true, true, true, -97, [4, 4284079342], [4, 4278192908], [4, 4278394139]]]], [12, "Label", 512, 33554432, 8, [[0, -98, [5, 194.0048828125, 41.8]], [47, "游戏说明 help", 30, 30, 30, false, true, true, true, -99, [4, 4288536406], [4, 4278588938]]]], [3, "traffic", 33554432, 1, [[0, -100, [5, 124.47400000000002, 298.7376]], [2, 0, -101, 6]], [1, -190.725, -174.653, 0]], [23, 0, {}, 1, [35, "98m1scCCpHFaAD50RjkFIZ", null, null, -104, [50, "3bBm5N8+NLeYdVfjOnRTcE", null, [[8, "loading", ["_name"], [1, ["98m1scCCpHFaAD50RjkFIZ"]]], [9, ["_lpos"], [1, ["98m1scCCpHFaAD50RjkFIZ"]], [1, -3.42999999999995, 1.3064999999999714, 0]], [9, ["_lrot"], [1, ["98m1scCCpHFaAD50RjkFIZ"]], [3, 0, 0, 0, 1]], [51, ["_euler"], -102, [1, 0, 0, 0]], [9, ["_contentSize"], [1, ["dftExsNEZHn4dtPomyMxdT"]], [5, 1675.126, 936.391]], [52, ["_spriteFrame"], [1, ["50p6T0zeZHzqrT8IjFBzum"]], 8], [9, ["_color"], [1, ["50p6T0zeZHzqrT8IjFBzum"]], [4, 4289835451]], [8, 0, ["_sizeMode"], [1, ["50p6T0zeZHzqrT8IjFBzum"]]], [8, 0, ["_type"], [1, ["50p6T0zeZHzqrT8IjFBzum"]]], [8, true, ["_isTrimmedMode"], [1, ["50p6T0zeZHzqrT8IjFBzum"]]], [53, true, ["_active"], -103]]], 7]], [3, "soundtext", 33554432, 3, [[0, -105, [5, 390.509033203125, 94.39999999999999]], [48, "打开或关闭声音\nturn on / turn off the sound effect", 24, 24.5, 2, true, true, true, -106, [4, 4287005403]]], [1, 0, 127.337, 0]], [25, "Label", 33554432, 11, [[[0, -107, [5, 91.19140625, 56.047999999999995]], -108], 4, 1]], [3, "title", 33554432, 4, [[0, -109, [5, 188.173583984375, 54.4]], [19, "help/帮助", 42.5, 42.5, true, true, true, -110, [4, 4286765690]]], [1, -7.99, 302.016, 0]], [3, "content", 33554432, 4, [[0, -111, [5, 779.6208437500001, 508.804]], [49, "不同的车辆拥有不同特性和武器，用出色的驾驶和甩尾技术在有限的时间内喷洒更多颜料！\n必要时，可使用车辆的专属武器攻击对手/移除对手已喷洒的燃料\n倒计时结束后，依据玩家颜料占比进行评分\nS:45%  A:35%  B:25%  F:<25%\n\nDifferent cars have different features and weapons. \nUse your excellent driving and drifting skills \nto spray more paint within the limited time! \nWhen necessary, use the exclusive weapon to attack your opponents or remove the fuel they have sprayed.\nAfter the countdown ends, players will be scored based on their proportion of paint. \nS:45%  A:35%  B:25%  F:<25%", 25.6, 25.6, 35.4, 3, true, true, true, -112, [4, 4292519892]]], [1, 5.428, -21.068, 0]], [16, "Label", 33554432, 12, [[0, -113, [5, 65.56640625, 56.047999999999995]], [20, "确认\nconfirm", 20, 20, 24.8, -114, [4, 4280967838]]]], [3, "text", 33554432, 5, [[0, -115, [5, 409.6884765625, 94.39999999999999]], [19, "确认要重制进度吗？\ncomfirm to reset player progress?", 25, 25, true, true, true, -116, [4, 4283001582]]], [1, 0, 139.085, 0]], [26, "Camera", 1, [-117], [1, 0, 0, 1000]], [54, 0, 381.4866760168303, 0, 1108344832, 29, [4, 4278190080]], [10, 3, 6, [4, 4292269782], 6], [10, 3, 7, [4, 4292269782], 7], [10, 3, 8, [4, 4292269782], 8], [10, 3, 9, [4, 4292269782], 9], [1, ["98m1scCCpHFaAD50RjkFIZ"]], [6, 3, 13], [20, "音效:开\nSound: on", 20, 20, 24.8, 24, [4, 4280967838]], [6, 3, 11], [6, 3, 14], [6, 3, 15], [6, 3, 12], [17, "<PERSON><PERSON><PERSON><PERSON>", "ef0ZsUXShJ9pBKMQh7xWv7", 10, [[55, -118]]], [17, "MainMenuController", "1eIGv73uxJ149Ou4e8BJTt", 10, [[56, -119, 31, 34, 36, 38, 3, 37, 33, 39, 4, 32, 5, 41, 40]]], [14, true, false, 2], [14, true, false, 2], [14, true, false, 2], [4, false, 2], [4, false, 2], [4, false, 2], [4, false, 2], [4, false, 2], [4, false, 2]], 0, [0, 0, 1, 0, 7, 30, 0, 0, 1, 0, 0, 1, 0, -1, 29, 0, -2, 16, 0, -3, 17, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 21, 0, -9, 22, 0, -10, 3, 0, -11, 4, 0, -12, 5, 0, 8, 52, 0, 9, 51, 0, 10, 50, 0, 11, 49, 0, 12, 48, 0, 13, 47, 0, 14, 46, 0, 15, 45, 0, 16, 44, 0, 0, 2, 0, -2, 50, 0, -3, 51, 0, -4, 49, 0, -5, 44, 0, -6, 45, 0, -7, 46, 0, -8, 47, 0, -9, 48, 0, -10, 52, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, -1, 23, 0, -2, 13, 0, -3, 11, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 25, 0, -3, 26, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, -2, 12, 0, -3, 28, 0, 0, 6, 0, 0, 6, 0, -3, 31, 0, -1, 18, 0, 0, 7, 0, 0, 7, 0, -3, 32, 0, -1, 19, 0, 0, 8, 0, 0, 8, 0, -3, 33, 0, -1, 20, 0, 0, 9, 0, 0, 9, 0, -3, 34, 0, 0, 9, 0, -1, 22, 0, -2, 42, 0, -3, 43, 0, 0, 11, 0, 0, 11, 0, -3, 38, 0, -1, 24, 0, 0, 12, 0, 0, 12, 0, -3, 41, 0, -1, 27, 0, 0, 13, 0, 0, 13, 0, -3, 36, 0, 0, 14, 0, 0, 14, 0, -3, 39, 0, 0, 15, 0, 0, 15, 0, -3, 40, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 5, 35, 0, 5, 35, 0, 17, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -2, 37, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, -1, 30, 0, 0, 42, 0, 0, 43, 0, 18, 10, 1, 6, 10, 2, 6, 10, 119], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 32, 33, 44, 45, 46, 47, 48, 49, 50, 51, 52], [1, 1, 1, 1, 1, 1, 1, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2], [4, 9, 0, 1, 5, 10, 11, 12, 4, 2, 0, 3, 2, 3, 2, 1, 3, 13, 14, 15, 0, 1, 5, 16, 17, 18, 19, 20, 21, 22, 23, 24]], [[{"name": "logo", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [25]], [[{"name": "79850dc31e45b5179163b46b25a182d1", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [3], [26]]]]