[1, ["a5AYHp7L1NNr9TPbUU8k/u@f9941", "5cJwJ4wJdNup8V84hmg7/3@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "4b790l5jJOfqo0gZeDZAGG@f9941", "93aKPiKb9K06Qid6CCQlRL@f9941", "28l0vD8uRKgaQC1xbRfBmn@f9941", "9f2QDdIhtPiY8s+6NCQ8g1@f9941", "24pwTaKGdEbY0aXpIMdeCd@f9941", "0dVhScMXdKjY6IhDQiRjcO@f9941", "8blPE3oJpLwaLoz+m5OBqB@f9941", "c3BkztNJNADprvldzxJ8aR@f9941", "363Wg+uupMso3BZClEmzqv@f9941", "0cDsMdqN1Mj6klZ8MqIeT2@f9941", "57UkdL/AlF0Y8EGScnAyuD@f9941", "0cL/pExSVCJJTyiVUFvr4v@f9941", "7bzlqIK8NFS5ZUlrYuHKKf@f9941", "4c8ZSK/8pCbpxDHnyGShtg@f9941", "a4emATuO9PdopwCl0PK6gb@f9941", "cf9FGAGXNKU42eeRz48ELY@f9941", "2eY9bbyBRJlJmqIrqHyQe+@f9941", "35mQIG8fdJwJHWpQkh/IEo@f9941", "53CMyA/N5GZ7qm283If9Ey@f9941", "a96XsRFJ9G5qmQyV/W2RzX@f9941", "0bM9w4DBNPVbdUJ9dmENtZ@f9941", "7eleAytq5N6airyBsktftf@f9941", "59OQTy+oVJj5sOn3mJDCT9@f9941", "a4JsLIWLFOWqFs51BhotA0@f9941", "6aAXnbsLxJBYlTirB2o8/W@f9941", "81Ugz1OgJGO7PsyIcKP0lA", "0bM9w4DBNPVbdUJ9dmENtZ@6c48a", "0cDsMdqN1Mj6klZ8MqIeT2@6c48a", "0dVhScMXdKjY6IhDQiRjcO@6c48a", "20g1ukYUVPvKWKBRznAKo+@6c48a", "28l0vD8uRKgaQC1xbRfBmn@6c48a", "35mQIG8fdJwJHWpQkh/IEo@6c48a", "363Wg+uupMso3BZClEmzqv@6c48a", "4b790l5jJOfqo0gZeDZAGG@6c48a", "4c8ZSK/8pCbpxDHnyGShtg@6c48a", "53CMyA/N5GZ7qm283If9Ey@6c48a", "54TknWPwVPqJqeCR+Y/Czo@6c48a", "57UkdL/AlF0Y8EGScnAyuD@6c48a", "59OQTy+oVJj5sOn3mJDCT9@6c48a", "5cJwJ4wJdNup8V84hmg7/3@6c48a", "6aAXnbsLxJBYlTirB2o8/W@6c48a", "7eleAytq5N6airyBsktftf@6c48a", "8blPE3oJpLwaLoz+m5OBqB@6c48a", "93aKPiKb9K06Qid6CCQlRL@6c48a", "95EkngnxZFbYuFpsqVTaFr@6c48a", "a4JsLIWLFOWqFs51BhotA0@6c48a", "a4emATuO9PdopwCl0PK6gb@6c48a", "a5AYHp7L1NNr9TPbUU8k/u@6c48a", "a96XsRFJ9G5qmQyV/W2RzX@6c48a", "c3BkztNJNADprvldzxJ8aR@6c48a", "cf9FGAGXNKU42eeRz48ELY@6c48a"], ["node", "_spriteFrame", "_textureSource", "_target", "_checkMark", "_normalSprite", "_parent", "_hoverSprite", "_pressedSprite", "_disabledSprite", "_cameraComponent", "confirmButton", "closeButton", "infoLabel", "priceLabel", "root", "scene", "asset", "value"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_id", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_components", "_parent", "_children", "_lpos", "_lscale", "_lrot", "_euler", "_prefab"], -3, 9, 1, 2, 5, 5, 5, 5, 4], ["cc.Label", ["_string", "_actualFontSize", "_enableOutline", "_fontSize", "_enableShadow", "_isBold", "_overflow", "_lineHeight", "_enableWrapText", "_outlineWidth", "node", "_color", "_outlineColor"], -7, 1, 5, 5], ["cc.Node", ["_name", "_layer", "_active", "_components", "_lpos", "_parent", "_children", "_lscale"], 0, 12, 5, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_spriteFrame", "_color"], 1, 1, 6, 5], ["cc.<PERSON><PERSON>", ["_transition", "_enabled", "node", "_normalColor", "_target", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite"], 1, 1, 5, 1, 6, 6, 6, 6], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_alignMode", "_left", "_isAbsLeft", "_isAbsTop", "_originalHeight", "node", "_target"], -5, 1, 1], ["cc.Layout", ["_layoutType", "_resizeMode", "_spacingX", "_isAlign", "_paddingLeft", "_paddingRight", "_paddingTop", "_paddingBottom", "_spacingY", "node"], -6, 1], ["cc.Toggle", ["_isChecked", "node", "_checkMark", "_target"], 2, 1, 1, 1], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["c4e94PebKNGoJIxF8tD6ORW", ["node", "priceLabel", "infoLabel", "closeButton", "confirmButton"], 3, 1, 1, 1, 1, 1], ["cc.TargetInfo", ["localID"], 2], ["<PERSON><PERSON>", ["bounceDuration", "brake", "vertical", "node", "_content"], 0, 1, 1], ["cc.Mask", ["node"], 3, 1], ["cc.Graphics", ["node", "_fillColor"], 3, 1, 5], ["cc.Scene", ["_name", "autoReleaseAssets", "_children", "_prefab", "_globals"], 1, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -2, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_far", "_visibility", "node", "_color"], -2, 1, 5], ["cc.ToggleContainer", ["_allowSwitchOff", "node"], 2, 1], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "_barSprite"], 1, 1, 1], ["add91PL8SRFs5KSXc8GbptH", ["node", "speedProgressBar", "steeringProgressBar", "durabilityProgressBar"], 3, 1, 1, 1, 1], ["66fbclPUjBL7oYA2xIdTwf+", ["node", "moneyLabel"], 3, 1, 1], ["be7b23A2jVN6agcMGkP3NKP", ["node", "levelToggleGroup", "carToggleGroup", "startButton", "backButton", "insufficientMoneyLabel", "purchasePanelNode", "carPropertyDisplay"], 3, 1, 1, 1, 1, 1, 1, 1, 1]], [[6, 0, 1, 1], [1, 0, 1, 7, 8, 6, 9, 3], [4, 1, 0, 2, 3, 3], [4, 0, 2, 3, 2], [1, 0, 1, 7, 6, 3], [4, 0, 2, 2], [1, 0, 1, 7, 6, 9, 3], [3, 0, 1, 5, 3, 4, 3], [2, 0, 1, 3, 5, 2, 4, 10, 11, 7], [1, 0, 1, 7, 6, 9, 11, 12, 3], [4, 2, 3, 1], [9, 0, 1, 2, 2], [14, 0, 2], [3, 0, 2, 1, 5, 3, 7, 4], [6, 0, 1, 2, 1], [4, 1, 0, 2, 4, 3, 3], [5, 1, 0, 2, 3, 4, 5, 6, 7, 8, 3], [5, 0, 2, 3, 4, 2], [1, 0, 1, 7, 6, 10, 3], [3, 0, 1, 5, 6, 3, 4, 3], [3, 0, 2, 1, 5, 3, 4, 4], [35, 0, 1, 2, 3], [1, 0, 1, 7, 8, 6, 3], [3, 0, 1, 6, 3, 4, 7, 3], [4, 1, 0, 2, 4, 3], [9, 0, 1, 3, 2, 2], [32, 0, 1, 2, 2], [38, 0, 1, 2, 3, 3], [1, 0, 2, 7, 6, 3], [3, 0, 1, 6, 3, 4, 3], [5, 0, 2, 3, 4, 5, 2], [5, 0, 2, 2], [15, 0, 1, 2, 3, 4, 4], [16, 0, 1], [17, 0, 1, 1], [2, 0, 1, 3, 2, 4, 10, 11, 6], [31, 0, 1, 2, 3], [37, 0, 1, 2], [10, 0, 2], [1, 0, 1, 2, 8, 6, 9, 4], [1, 0, 3, 1, 7, 8, 6, 4], [1, 0, 3, 1, 7, 6, 10, 4], [1, 4, 5, 7, 13, 3], [11, 0, 1, 2, 3, 2], [6, 0, 1], [12, 0, 1, 1], [7, 0, 1, 2, 3, 8, 5], [7, 0, 4, 1, 2, 5, 6, 7, 8, 9, 8], [7, 8, 1], [8, 1, 0, 2, 3, 9, 5], [8, 1, 0, 4, 5, 2, 3, 9, 7], [8, 0, 6, 7, 8, 9, 5], [9, 1, 3, 2, 1], [13, 0, 1, 2, 3, 4, 1], [18, 0, 1, 2, 3, 4, 3], [19, 0, 1, 2, 3, 4, 5, 6], [20, 0, 1, 2, 3, 4, 5, 4], [21, 0, 1, 2, 3, 4, 5, 6, 7, 1], [22, 0, 1, 1], [23, 0, 1, 1], [24, 1], [25, 1], [26, 1], [27, 0, 2], [28, 1], [29, 1], [2, 0, 1, 6, 8, 2, 10, 11, 6], [2, 0, 1, 6, 8, 5, 2, 4, 10, 11, 8], [2, 0, 1, 3, 7, 10, 11, 5], [2, 0, 1, 3, 5, 2, 9, 4, 10, 11, 12, 8], [2, 0, 1, 3, 7, 6, 5, 2, 9, 4, 10, 11, 12, 10], [2, 0, 1, 3, 7, 5, 2, 10, 11, 7], [30, 0, 1, 2, 3], [33, 0, 1, 2, 2], [34, 0, 1, 2, 2], [36, 0, 1, 2, 3, 4, 5, 6, 6], [39, 0, 1, 2, 3, 1], [40, 0, 1, 1], [41, 0, 1, 2, 3, 4, 5, 6, 7, 1]], [[[[38, "LevelSelect"], [39, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [[0, -1, [5, 1280, 720]], [45, -3, -2], [46, 45, 5.684341886080802e-14, 5.684341886080802e-14, 1, -4]], [1, 640, 360.00000000000006, 0]], [29, "levels", 33554432, [-19, -20, -21, -22, -23], [[[0, -16, [5, 1692, 219.869]], [49, 1, 1, 48, true, -17], -18], 4, 4, 1], [1, 338.362, 10.544, 0]], [1, "level-1", 33554432, 2, [-31, -32, -33], [[0, -24, [5, 300, 200]], [2, 1, 0, -25, 4], [16, false, 2, -27, [4, 4292269782], -26, 5, 6, 7, 8], [52, -30, -29, -28]], [1, -696, 0, 0]], [1, "level-2", 33554432, 2, [-41, -42, -43], [[0, -34, [5, 300, 200]], [2, 1, 0, -35, 10], [16, false, 2, -37, [4, 4292269782], -36, 11, 12, 13, 14], [25, false, -40, -39, -38]], [1, -348, 0, 0]], [29, "cars", 33554432, [-47, -48, -49, -50, -51], [[[0, -44, [5, 1232, 120]], [50, 1, 1, 20, 20, 48, true, -45], -46], 4, 4, 1], [1, 268.938, 0, 0]], [22, "level-3", 33554432, 2, [-58, -59, -60], [[0, -52, [5, 300, 200]], [2, 1, 0, -53, 16], [16, false, 2, -55, [4, 4292269782], -54, 17, 18, 19, 20], [11, false, -57, -56]]], [1, "level-4", 33554432, 2, [-67, -68, -69], [[0, -61, [5, 300, 200]], [2, 1, 0, -62, 22], [16, false, 2, -64, [4, 4292269782], -63, 23, 24, 25, 26], [11, false, -66, -65]], [1, 348, 0, 0]], [1, "level-5", 33554432, 2, [-76, -77, -78], [[0, -70, [5, 300, 200]], [2, 1, 0, -71, 28], [16, false, 2, -73, [4, 4292269782], -72, 29, 30, 31, 32], [11, false, -75, -74]], [1, 696, 0, 0]], [40, "PurchasePanel", false, 33554432, 1, [-86, -87, -88, -89], [[0, -79, [5, 509, 558]], [10, -80, 60], [53, -85, -84, -83, -82, -81]]], [1, "car-1", 33554432, 5, [-95, -96, -97], [[0, -90, [5, 200, 124]], [2, 1, 0, -91, 37], [25, false, -94, -93, -92]], [1, -496, 0, 0]], [1, "car-2", 33554432, 5, [-103, -104, -105], [[0, -98, [5, 200, 124]], [2, 1, 0, -99, 40], [25, false, -102, -101, -100]], [1, -248, 0, 0]], [19, "car-property", 33554432, 1, [-110, -111, -112], [[[0, -106, [5, 334.683, 200]], [51, 2, 23.2, 1, 20.1, -107], [3, 0, -108, 57], -109], 4, 4, 4, 1], [1, 387.341, -135.665, 0]], [22, "car-3", 33554432, 5, [-117, -118, -119], [[0, -113, [5, 200, 124]], [2, 1, 0, -114, 44], [11, false, -116, -115]]], [1, "car-4", 33554432, 5, [-124, -125, -126], [[0, -120, [5, 200, 124]], [2, 1, 0, -121, 47], [11, false, -123, -122]], [1, 248, 0, 0]], [1, "car-5", 33554432, 5, [-131, -132, -133], [[0, -127, [5, 200, 124]], [2, 1, 0, -128, 50], [11, false, -130, -129]], [1, 496, 0, 0]], [1, "PlayerInfo", 33554432, 1, [-135, -136, -137, -138], [[44, -134]], [1, 0, 300, 0]], [22, "Pur<PERSON><PERSON><PERSON>on", 33554432, 10, [-143], [[0, -139, [5, 100, 40]], [2, 1, 0, -140, 34], [30, 3, -142, [4, 4292269782], -141, 35]]], [19, "startbutton", 33554432, 1, [-147], [[[0, -144, [5, 200, 80]], [15, 1, 0, -145, [4, 4292269782], 52], -146], 4, 4, 1], [1, 226.081, -292.553, 0]], [19, "backbutton", 33554432, 1, [-151], [[[0, -148, [5, 200, 80]], [15, 1, 0, -149, [4, 4292269782], 53], -150], 4, 4, 1], [1, -284.139, -292.553, 0]], [12, ["50p6T0zeZHzqrT8IjFBzum"]], [1, "level-scroll", 33554432, 1, [-155], [[0, -152, [5, 1111.749, 269.823]], [2, 1, 0, -153, 33], [32, 0.23, 0.75, false, -154, 2]], [1, 0, 118.547, 0]], [1, "view", 33554432, 21, [2], [[0, -156, [5, 1053.689, 265.336]], [33, -157], [34, -158, [4, 16777215]]], [1, 15.49, -7.668, 0]], [1, "cars-scroll", 33554432, 1, [-162], [[0, -159, [5, 731.981, 217.89299999999997]], [3, 0, -160, 51], [32, 0.23, 0.75, false, -161, 5]], [1, -191.026, -137.308, 0]], [1, "view", 33554432, 23, [5], [[0, -163, [5, 735.317, 250]], [33, -164], [34, -165, [4, 16777215]]], [1, 0.651, -4.356, 0]], [4, "Pur<PERSON><PERSON><PERSON>on", 33554432, 11, [[0, -166, [5, 100, 40]], [2, 1, 0, -167, 38], [17, 3, -169, [4, 4292269782], -168]]], [4, "Pur<PERSON><PERSON><PERSON>on", 33554432, 13, [[0, -170, [5, 100, 40]], [2, 1, 0, -171, 41], [30, 3, -173, [4, 4292269782], -172, 42]]], [4, "Pur<PERSON><PERSON><PERSON>on", 33554432, 14, [[0, -174, [5, 100, 40]], [2, 1, 0, -175, 45], [17, 3, -177, [4, 4292269782], -176]]], [4, "Pur<PERSON><PERSON><PERSON>on", 33554432, 15, [[0, -178, [5, 100, 40]], [2, 1, 0, -179, 48], [17, 3, -181, [4, 4292269782], -180]]], [23, "ProgressBar", 33554432, [-185], [[[0, -182, [5, 300, 15]], [15, 1, 0, -183, [4, 4291690441], 54], -184], 4, 4, 1], [1, 132.272, -2.471, 0], [1, 0.5, 0.5, 1]], [23, "ProgressBar", 33554432, [-189], [[[0, -186, [5, 300, 15]], [15, 1, 0, -187, [4, 4294937226], 55], -188], 4, 4, 1], [1, 132.439, -2.471, 0], [1, 0.5, 0.5, 1]], [23, "ProgressBar", 33554432, [-193], [[[0, -190, [5, 300, 15]], [15, 1, 0, -191, [4, 4294761983], 56], -192], 4, 4, 1], [1, 134.433, -2.471, 0], [1, 0.5, 0.5, 1]], [19, "BuyButton", 33554432, 9, [-197], [[[0, -194, [5, 280.995, 81.87200000000001]], [3, 0, -195, 59], -196], 4, 4, 1], [1, 0, -162.386, 0]], [54, "LevelSelect", true, [1, -199, -200], [55, null, null, "091c5c0e-b72a-4cad-ab68-635bc57ff236", null, null, [-198]], [57, [58, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [59, [4, 4283190348], [0, 512, 512]], [60], [61], [62], [63, false], [64], [65]]], [6, "BioPic", 33554432, 16, [[0, -201, [5, 73.494, 63.482]], [3, 0, -202, 1], [47, 9, 0.06891250000000006, 0.024312500000000025, 28.259, false, false, 63.482, -203, 1]], [1, -515.045, 10.754000000000005, 0]], [6, "name", 33554432, 16, [[0, -204, [5, 106.041015625, 54.4]], [35, "Player", 36, 36, true, true, -205, [4, 4294534399]], [48, -206]], [1, -414.915, 10, 0]], [1, "speed", 33554432, 12, [-208, 29], [[0, -207, [5, 100, 30]]], [1, -97.013, 61.800000000000004, 0]], [1, "turn", 33554432, 12, [-210, 30], [[0, -209, [5, 100, 30]]], [1, -97.013, 11.700000000000003, 0]], [1, "tough", 33554432, 12, [-212, 31], [[0, -211, [5, 100, 30]]], [1, -97.013, -38.4, 0]], [7, "close", 33554432, 9, [[[0, -213, [5, 55.50500000000002, 55.50500000000002]], [3, 0, -214, 58], -215], 4, 4, 1], [1, 212.483, 235.404, 0]], [4, "background2", 33554432, 1, [[0, -216, [5, 1595.403, 897.4141875]], [3, 0, -217, 0]]], [6, "coinIcon", 33554432, 16, [[14, -218, [5, 50, 50], [0, 1, 0.5]], [3, 0, -219, 2]], [1, 608.44, 12.801, 0]], [7, "moneytext", 33554432, 16, [[[14, -220, [5, 84.0859375, 54.4], [0, 1, 0.5]], -221], 4, 1], [1, 544.611, 10.241, 0]], [7, "mark", 33554432, 3, [[[0, -222, [5, 345.347, 251.854]], -223], 4, 1], [1, 4.177999999999999, -8.017500000000002, 0]], [6, "GradeLabel", 33554432, 3, [[0, -224, [5, 44, 54.4]], [8, "评价", 20, 20, true, true, true, -225, [4, 4282122153]]], [1, 129.724, -114.263, 0]], [41, "lock", false, 33554432, 3, [[0, -226, [5, 139, 186]], [10, -227, 3]], [1, 0.5, 0.5, 1]], [20, "mark", false, 33554432, 4, [[[0, -228, [5, 340.73, 262.754]], -229], 4, 1], [1, 3.7934999999999954, -10.041999999999991, 0]], [6, "GradeLabel", 33554432, 4, [[0, -230, [5, 44, 54.4]], [8, "评价", 20, 20, true, true, true, -231, [4, 4285398922]]], [1, 132.207, -114.219, 0]], [18, "lock", 33554432, 4, [[0, -232, [5, 139, 186]], [10, -233, 9]], [1, 0.5, 0.5, 1]], [20, "mark", false, 33554432, 6, [[[0, -234, [5, 340, 262]], -235], 4, 1], [1, 3.7934999999999954, -10.041999999999991, 0]], [6, "GradeLabel", 33554432, 6, [[0, -236, [5, 44, 54.4]], [8, "评价", 20, 20, true, true, true, -237, [4, 4286709694]]], [1, 130.409, -112.772, 0]], [18, "lock", 33554432, 6, [[0, -238, [5, 139, 186]], [10, -239, 15]], [1, 0.5, 0.5, 1]], [20, "mark", false, 33554432, 7, [[[0, -240, [5, 340, 262]], -241], 4, 1], [1, 3.7934999999999954, -10.041999999999991, 0]], [6, "GradeLabel", 33554432, 7, [[0, -242, [5, 44, 54.4]], [8, "评价", 20, 20, true, true, true, -243, [4, 4286709694]]], [1, 130.409, -112.772, 0]], [18, "lock", 33554432, 7, [[0, -244, [5, 139, 186]], [10, -245, 21]], [1, 0.5, 0.5, 1]], [20, "mark", false, 33554432, 8, [[[0, -246, [5, 340, 262]], -247], 4, 1], [1, 3.7934999999999954, -10.041999999999991, 0]], [6, "GradeLabel", 33554432, 8, [[0, -248, [5, 44, 54.4]], [8, "评价", 20, 20, true, true, true, -249, [4, 4286709694]]], [1, 130.409, -112.772, 0]], [18, "lock", 33554432, 8, [[0, -250, [5, 139, 186]], [10, -251, 27]], [1, 0.5, 0.5, 1]], [13, "mark", false, 33554432, 10, [[[0, -252, [5, 50, 50]], -253], 4, 1], [1, 1.8, 1.8, 1]], [9, "Label", 33554432, 17, [[0, -254, [5, 159.6053466796875, 54.4]], [8, "↓↓↓ FREE!!!! ", 25.7, 25.7, true, true, true, -255, [4, 4281328627]]], [1, -8.143, 74.903, 0], [3, 0, 0, 0.12186934340514748, 0.992546151641322], [1, 0, 0, 14]], [9, "weapon", 33554432, 10, [[0, -256, [5, 30, 60]], [3, 0, -257, 36]], [1, 0, -75.67, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [13, "mark", false, 33554432, 11, [[[0, -258, [5, 50, 50]], -259], 4, 1], [1, 1.8, 1.8, 1]], [9, "weapon", 33554432, 11, [[0, -260, [5, 50, 100]], [3, 0, -261, 39]], [1, 0, -71.394, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [13, "mark", false, 33554432, 13, [[[0, -262, [5, 50, 50]], -263], 4, 1], [1, 1.8, 1.8, 1]], [9, "weapon", 33554432, 13, [[0, -264, [5, 50, 50]], [3, 0, -265, 43]], [1, 0, -71.394, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [13, "mark", false, 33554432, 14, [[[0, -266, [5, 50, 50]], -267], 4, 1], [1, 1.8, 1.8, 1]], [9, "weapon", 33554432, 14, [[0, -268, [5, 50, 100]], [3, 0, -269, 46]], [1, 0, -71.394, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [13, "mark", false, 33554432, 15, [[[0, -270, [5, 50, 50]], -271], 4, 1], [1, 1.8, 1.8, 1]], [9, "weapon", 33554432, 15, [[0, -272, [5, 50, 50]], [3, 0, -273, 49]], [1, 0, -71.394, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [4, "Label", 33554432, 18, [[0, -274, [5, 200, 80]], [66, "start", 40, 1, false, true, -275, [4, 4287892735]]]], [4, "Label", 33554432, 19, [[0, -276, [5, 200, 80]], [67, "back", 40, 1, false, true, true, true, -277, [4, 4286250986]]]], [4, "speed", 33554432, 36, [[0, -278, [5, 101.802734375, 54.4]], [8, "speed速度", 20, 20, true, true, true, -279, [4, 4278190335]]]], [7, "Bar", 33554432, 29, [[[14, -280, [5, 0, 15], [0, 0, 0.5]], -281], 4, 1], [1, -150, 0, 0]], [6, "turn", 33554432, 37, [[0, -282, [5, 82.876953125, 54.4]], [8, "turn转向", 20, 20, true, true, true, -283, [4, 4284937215]]], [1, -9.216, 0, 0]], [7, "Bar", 33554432, 30, [[[14, -284, [5, 0, 15], [0, 0, 0.5]], -285], 4, 1], [1, -150, 0, 0]], [4, "tough", 33554432, 38, [[0, -286, [5, 99.52734375, 54.4]], [8, "tough坚硬", 20, 20, true, true, true, -287, [4, 4278255370]]]], [7, "Bar", 33554432, 31, [[[14, -288, [5, 0, 15], [0, 0, 0.5]], -289], 4, 1], [1, -150, 0, 0]], [4, "Label", 33554432, 32, [[0, -290, [5, 181.181640625, 56.047999999999995]], [68, "确认购买\npruchase comfirmed", 20, 20, 24.8, -291, [4, 4280967838]]]], [7, "PriceText", 33554432, 9, [[[0, -292, [5, 240.215234375, 99.99999999999999]], -293], 4, 1], [1, 0, 204.572, 0]], [7, "infoText", 33554432, 9, [[[0, -294, [5, 482.38964013671875, 140.66799999999998]], -295], 4, 1], [1, 2.697, 81.515, 0]], [7, "notice", 33554432, 1, [[[0, -296, [5, 370.26416015625, 94.852]], -297], 4, 1], [1, -32.114, 295.904, 0]], [42, 0, {}, 1, [56, "98m1scCCpHFaAD50RjkFIZ", null, null, -298, [72, "bbGyGdOA5B3ZJj3RjG/RpL", null, [[36, "loading", ["_name"], [12, ["98m1scCCpHFaAD50RjkFIZ"]]], [26, ["_lpos"], [12, ["98m1scCCpHFaAD50RjkFIZ"]], [1, 0, 17.948999999999955, 0]], [26, ["_lrot"], [12, ["98m1scCCpHFaAD50RjkFIZ"]], [3, 0, 0, 0, 1]], [26, ["_euler"], [12, ["98m1scCCpHFaAD50RjkFIZ"]], [1, 0, 0, 0]], [73, ["_color"], 20, [4, 4290032820]], [36, true, ["_active"], [12, ["98m1scCCpHFaAD50RjkFIZ"]]], [74, ["_spriteFrame"], 20, 62], [21, 0, ["_type"], 20], [21, 0, ["_fillRange"], 20], [21, 0, ["_fillStart"], 20], [21, 2, ["_fillType"], 20]]], 61]], [43, "Camera", 1, [-299], [1, 0, 0, 1000]], [75, 0, 381.4866760168303, 0, 2000, 1108344832, 82, [4, 4278190080]], [35, "9999", 36, 36, true, true, 42, [4, 4152421375]], [5, 0, 43], [5, 0, 46], [5, 0, 49], [5, 0, 52], [5, 0, 55], [37, true, 2], [5, 0, 58], [5, 0, 61], [5, 0, 63], [5, 0, 65], [5, 0, 67], [37, true, 5], [17, 3, 18, [4, 4292269782], 18], [17, 3, 19, [4, 4292269782], 19], [24, 1, 0, 72, [4, 4283124730]], [27, 300, 0, 29, 99], [24, 1, 0, 74, [4, 4288936959]], [27, 300, 0, 30, 101], [24, 1, 0, 76, [4, 4285267866]], [27, 300, 0, 31, 103], [76, 12, 100, 102, 104], [31, 3, 39], [31, 3, 32], [69, "该车辆价格为：1000\n是否确认购买？", 25, 25, true, true, 4.8, true, 78, [4, 4290118872], [4, 4291624704]], [70, "This super maneuverable car is equipped with a bullet launcher. When you hit your opponent, you can cause damage.", 25, 25, 31.8, 3, true, true, 2.6, true, 79, [4, 4290118872], [4, 4281124846]], [71, "拥有的金币不够\nyour money is not enough", 29.3, 29.3, 40.2, true, true, 80, [4, 4292546731]], [28, "UIManager", "1aPrVcYy1KLKgnxDGm5GUZ", 33, [[77, -300, 84]]], [28, "SelectManager", "camwNZlClA7a7gD6K8R87I", 33, [[78, -301, 90, 96, 97, 98, 110, 9, 105]]]], 0, [0, 0, 1, 0, 10, 83, 0, 0, 1, 0, 0, 1, 0, -1, 82, 0, -2, 40, 0, -3, 16, 0, -4, 21, 0, -5, 23, 0, -6, 18, 0, -7, 19, 0, -8, 12, 0, -9, 9, 0, -10, 80, 0, -11, 81, 0, 0, 2, 0, 0, 2, 0, -3, 90, 0, -1, 3, 0, -2, 4, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, 0, 3, 0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 4, 85, 0, 3, 3, 0, 0, 3, 0, -1, 43, 0, -2, 44, 0, -3, 45, 0, 0, 4, 0, 0, 4, 0, 3, 4, 0, 0, 4, 0, 4, 86, 0, 3, 4, 0, 0, 4, 0, -1, 46, 0, -2, 47, 0, -3, 48, 0, 0, 5, 0, 0, 5, 0, -3, 96, 0, -1, 10, 0, -2, 11, 0, -3, 13, 0, -4, 14, 0, -5, 15, 0, 0, 6, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, 4, 87, 0, 0, 6, 0, -1, 49, 0, -2, 50, 0, -3, 51, 0, 0, 7, 0, 0, 7, 0, 3, 7, 0, 0, 7, 0, 4, 88, 0, 0, 7, 0, -1, 52, 0, -2, 53, 0, -3, 54, 0, 0, 8, 0, 0, 8, 0, 3, 8, 0, 0, 8, 0, 4, 89, 0, 0, 8, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, 0, 9, 0, 0, 9, 0, 11, 107, 0, 12, 106, 0, 13, 109, 0, 14, 108, 0, 0, 9, 0, -1, 39, 0, -2, 32, 0, -3, 78, 0, -4, 79, 0, 0, 10, 0, 0, 10, 0, 4, 91, 0, 3, 10, 0, 0, 10, 0, -1, 58, 0, -2, 17, 0, -3, 60, 0, 0, 11, 0, 0, 11, 0, 4, 92, 0, 3, 11, 0, 0, 11, 0, -1, 61, 0, -2, 25, 0, -3, 62, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -4, 105, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, 0, 13, 0, 0, 13, 0, 4, 93, 0, 0, 13, 0, -1, 63, 0, -2, 26, 0, -3, 64, 0, 0, 14, 0, 0, 14, 0, 4, 94, 0, 0, 14, 0, -1, 65, 0, -2, 27, 0, -3, 66, 0, 0, 15, 0, 0, 15, 0, 4, 95, 0, 0, 15, 0, -1, 67, 0, -2, 28, 0, -3, 68, 0, 0, 16, 0, -1, 34, 0, -2, 35, 0, -3, 41, 0, -4, 42, 0, 0, 17, 0, 0, 17, 0, 3, 17, 0, 0, 17, 0, -1, 59, 0, 0, 18, 0, 0, 18, 0, -3, 97, 0, -1, 69, 0, 0, 19, 0, 0, 19, 0, -3, 98, 0, -1, 70, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, -1, 24, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 3, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 3, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 3, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 3, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, -3, 100, 0, -1, 72, 0, 0, 30, 0, 0, 30, 0, -3, 102, 0, -1, 74, 0, 0, 31, 0, 0, 31, 0, -3, 104, 0, -1, 76, 0, 0, 32, 0, 0, 32, 0, -3, 107, 0, -1, 77, 0, -1, 81, 0, -2, 111, 0, -3, 112, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, -1, 71, 0, 0, 37, 0, -1, 73, 0, 0, 38, 0, -1, 75, 0, 0, 39, 0, 0, 39, 0, -3, 106, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, -2, 84, 0, 0, 43, 0, -2, 85, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, -2, 86, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, -2, 87, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, -2, 88, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, -2, 89, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, -2, 91, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, -2, 92, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, -2, 93, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, -2, 94, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, -2, 95, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, -2, 99, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, -2, 101, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, -2, 103, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, -2, 108, 0, 0, 79, 0, -2, 109, 0, 0, 80, 0, -2, 110, 0, 15, 81, 0, -1, 83, 0, 0, 111, 0, 0, 112, 0, 16, 33, 1, 6, 33, 2, 6, 22, 5, 6, 24, 29, 6, 36, 30, 6, 37, 31, 6, 38, 301], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 99, 101, 103], [1, 1, 1, 1, 1, 5, 7, 8, 9, 1, 1, 5, 7, 8, 9, 1, 1, 5, 7, 8, 9, 1, 1, 5, 7, 8, 9, 1, 1, 5, 7, 8, 9, 1, 1, 5, 1, 1, 1, 1, 1, 1, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [10, 19, 20, 1, 11, 11, 2, 3, 4, 1, 12, 12, 2, 3, 4, 1, 13, 13, 2, 3, 4, 1, 14, 14, 2, 3, 4, 1, 15, 15, 2, 3, 4, 7, 0, 0, 21, 22, 0, 16, 23, 0, 0, 17, 24, 0, 16, 25, 0, 17, 26, 7, 18, 27, 8, 8, 8, 7, 28, 18, 29, 30, 10, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 9, 9, 9]], [[{"name": "rx7-f", "rect": {"x": 0, "y": 0, "width": 422, "height": 239}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 422, "height": 239}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-211, -119.5, 0, 211, -119.5, 0, -211, 119.5, 0, 211, 119.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 239, 422, 239, 0, 0, 422, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -211, "y": -119.5, "z": 0}, "maxPos": {"x": 211, "y": 119.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [31]], [[{"name": "ad8efe4d6bafcec5971648db598c0a71", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [32]], [[{"name": "416fa0011fee05baf8fe72f050036f62", "rect": {"x": 0, "y": 0, "width": 5248, "height": 2944}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 5248, "height": 2944}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2624, -1472, 0, 2624, -1472, 0, -2624, 1472, 0, 2624, 1472, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2944, 5248, 2944, 0, 0, 5248, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2624, "y": -1472, "z": 0}, "maxPos": {"x": 2624, "y": 1472, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [33]], [[{"name": "default_btn_normal", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [34]], [[{"name": "cs-ba", "rect": {"x": 0, "y": 128, "width": 999, "height": 310}, "offset": {"x": -0.5, "y": 21}, "originalSize": {"width": 1000, "height": 608}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-499.5, -155, 0, 499.5, -155, 0, -499.5, 155, 0, 499.5, 155, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 480, 999, 480, 0, 170, 999, 170], "nuv": [0, 0.27960526315789475, 0.999, 0.27960526315789475, 0, 0.7894736842105263, 0.999, 0.7894736842105263], "minPos": {"x": -499.5, "y": -155, "z": 0}, "maxPos": {"x": 499.5, "y": 155, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [35]], [[{"name": "mini-f", "rect": {"x": 0, "y": 0, "width": 862, "height": 564}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 862, "height": 564}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-431, -282, 0, 431, -282, 0, -431, 282, 0, 431, 282, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 564, 862, 564, 0, 0, 862, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -431, "y": -282, "z": 0}, "maxPos": {"x": 431, "y": 282, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [36]], [[{"name": "44f2669bbe90718e7c82cf002a71f346", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [37]], [[{"name": "select", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [38]], [[{"name": "red", "rect": {"x": 0, "y": 0, "width": 117, "height": 187}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 117, "height": 187}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-58.5, -93.5, 0, 58.5, -93.5, 0, -58.5, 93.5, 0, 58.5, 93.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 187, 117, 187, 0, 0, 117, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -58.5, "y": -93.5, "z": 0}, "maxPos": {"x": 58.5, "y": 93.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [39]], [[{"name": "z370", "rect": {"x": 0, "y": 0, "width": 473, "height": 264}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 473, "height": 264}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-236.5, -132, 0, 236.5, -132, 0, -236.5, 132, 0, 236.5, 132, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 264, 473, 264, 0, 0, 473, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -236.5, "y": -132, "z": 0}, "maxPos": {"x": 236.5, "y": 132, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [40]], [[{"name": "default_btn_pressed", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [41]], [[{"name": "ab128ccde06826881ab545f5092053a3", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [42]], [[{"name": "yellow", "rect": {"x": 0, "y": 0, "width": 130, "height": 194}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 130, "height": 194}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-65, -97, 0, 65, -97, 0, -65, 97, 0, 65, 97, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 194, 130, 194, 0, 0, 130, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -65, "y": -97, "z": 0}, "maxPos": {"x": 65, "y": 97, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [43]], [[{"name": "lock", "rect": {"x": 28, "y": 7, "width": 139, "height": 186}, "offset": {"x": -2.5, "y": 0}, "originalSize": {"width": 200, "height": 200}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-69.5, -93, 0, 69.5, -93, 0, -69.5, 93, 0, 69.5, 93, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [28, 193, 167, 193, 28, 7, 167, 7], "nuv": [0.14, 0.035, 0.835, 0.035, 0.14, 0.965, 0.835, 0.965], "minPos": {"x": -69.5, "y": -93, "z": 0}, "maxPos": {"x": 69.5, "y": 93, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [44]], [[{"name": "panel", "rect": {"x": 8, "y": 2, "width": 509, "height": 558}, "offset": {"x": -0.5, "y": -0.5}, "originalSize": {"width": 526, "height": 561}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-254.5, -279, 0, 254.5, -279, 0, -254.5, 279, 0, 254.5, 279, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [8, 559, 517, 559, 8, 1, 517, 1], "nuv": [0.015209125475285171, 0.0017825311942959, 0.9828897338403042, 0.0017825311942959, 0.015209125475285171, 0.9964349376114082, 0.9828897338403042, 0.9964349376114082], "minPos": {"x": -254.5, "y": -279, "z": 0}, "maxPos": {"x": 254.5, "y": 279, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [45]], [[{"name": "86-f", "rect": {"x": 0, "y": 0, "width": 419, "height": 256}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 419, "height": 256}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-209.5, -128, 0, 209.5, -128, 0, -209.5, 128, 0, 209.5, 128, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 256, 419, 256, 0, 0, 419, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -209.5, "y": -128, "z": 0}, "maxPos": {"x": 209.5, "y": 128, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [46]], [[{"name": "ee870d916fd79516ca72b3c8388529c5", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [47]], [[{"name": "select2", "rect": {"x": 0, "y": 0, "width": 617, "height": 596}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 617, "height": 596}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-308.5, -298, 0, 308.5, -298, 0, -308.5, 298, 0, 308.5, 298, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 596, 617, 596, 0, 0, 617, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -308.5, "y": -298, "z": 0}, "maxPos": {"x": 308.5, "y": 298, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [48]], [[{"name": "default_btn_disabled", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [49]], [[{"name": "close", "rect": {"x": 0, "y": 0, "width": 507, "height": 507}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 507, "height": 507}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-253.5, -253.5, 0, 253.5, -253.5, 0, -253.5, 253.5, 0, 253.5, 253.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 507, 507, 507, 0, 0, 507, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -253.5, "y": -253.5, "z": 0}, "maxPos": {"x": 253.5, "y": 253.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [50]], [[{"name": "helmet", "rect": {"x": 5, "y": 5, "width": 190, "height": 176}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 200, "height": 186}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-95, -88, 0, 95, -88, 0, -95, 88, 0, 95, 88, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [5, 181, 195, 181, 5, 5, 195, 5], "nuv": [0.025, 0.026881720430107527, 0.975, 0.026881720430107527, 0.025, 0.9731182795698925, 0.975, 0.9731182795698925], "minPos": {"x": -95, "y": -88, "z": 0}, "maxPos": {"x": 95, "y": 88, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [51]], [[{"name": "purchase", "rect": {"x": 0, "y": 0, "width": 797, "height": 357}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 797, "height": 357}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-398.5, -178.5, 0, 398.5, -178.5, 0, -398.5, 178.5, 0, 398.5, 178.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 357, 797, 357, 0, 0, 797, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -398.5, "y": -178.5, "z": 0}, "maxPos": {"x": 398.5, "y": 178.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [52]], [[{"name": "taycan-f", "rect": {"x": 0, "y": 0, "width": 586, "height": 303}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 586, "height": 303}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-293, -151.5, 0, 293, -151.5, 0, -293, 151.5, 0, 293, 151.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 303, 586, 303, 0, 0, 586, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -293, "y": -151.5, "z": 0}, "maxPos": {"x": 293, "y": 151.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [53]], [[{"name": "bf51dacb6999bf07f5c60c2c10c7e272", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [54]], [[{"name": "moeny", "rect": {"x": 0, "y": 0, "width": 200, "height": 145}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 200, "height": 145}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-100, -72.5, 0, 100, -72.5, 0, -100, 72.5, 0, 100, 72.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 145, 200, 145, 0, 0, 200, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -100, "y": -72.5, "z": 0}, "maxPos": {"x": 100, "y": 72.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [55]]]]