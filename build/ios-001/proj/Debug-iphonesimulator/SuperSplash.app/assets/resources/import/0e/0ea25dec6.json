[1, ["5fpKAgrRJNUZ59V87NwFbK", "86CHtLQoRLkYcH+h9xQG6h@f9941", "ff3ACRArVH6Zy6dSrQ9/Jg", "525Wn55e9KfJgHyymrwV2G", "7f46LBRONF7aOkgekKaZTH", "4942ZUssNH2a/rXbKChqGw@f9941", "02mF77AhdN+LUmE3XYF0kl@6c48a", "4dUynpj2dD46OZ4PqaWtX0", "b96V8CSuxMAYNDHGzgNrDG@f9941", "38QUxvzghAw7CgTyTOEDHe@f9941", "02mF77AhdN+LUmE3XYF0kl@f9941", "b7HBnQSslNy6oytl/eLEBq", "a4Yof/90tAdaSDCLECf+ct", "35QYurHgpBCKJnzoyMJCLD", "46ZtbII3ZN9L4o1m44O6rO"], ["node", "targetInfo", "root", "asset", "_spriteFrame", "target", "source", "_parent", "destroyedSprite", "paintPrefab", "normalBulletPrefab", "dartPrefab", "rocketPrefab", "_textureSource", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_prefab", "_components", "_parent", "_children", "_lpos"], -2, 4, 9, 1, 2, 5], ["91c082Rgh1HBbV2vKQl2J1W", ["maxSpeed", "acceleration", "maxHealth", "paintSprayInterval", "fireRate", "weaponType", "node", "__prefab"], -3, 1, 4], ["cc.Node", ["_name", "_mobility", "_layer", "_children", "_components", "_prefab", "_lpos", "_lrot", "_euler", "_lscale"], 0, 2, 12, 4, 5, 5, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.BoxCollider2D", ["_friction", "node", "__prefab", "_size", "_offset"], 2, 1, 4, 5, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo"], 2, 1, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.RigidBody2D", ["enabledContactListener", "_type", "_gravityScale", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8]], [[8, 0, 2], [13, 0, 2], [17, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 2, 3, 7, 5, 3], [4, 0, 1, 2, 3, 2], [11, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 2], [16, 0, 1, 2, 3], [18, 0, 1, 2, 3], [5, 1, 2, 4, 3, 1], [5, 0, 1, 2, 3, 2], [12, 0, 1, 2, 3, 2], [14, 0, 1, 2, 3, 4, 4], [19, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 4], [2, 0, 1, 2, 3, 4, 5, 6, 7, 9, 8, 4], [3, 0, 1, 1], [1, 0, 1, 2, 3, 6, 7, 5], [7, 0, 2], [0, 0, 1, 8, 6, 5, 3], [0, 0, 1, 7, 8, 6, 5, 9, 3], [0, 0, 4, 1, 7, 6, 5, 9, 4], [0, 0, 1, 7, 6, 5, 3], [4, 0, 1, 2, 2], [9, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 5, 6, 7, 7], [1, 0, 1, 2, 4, 5, 6, 7, 6]], [[[{"name": "snow", "rect": {"x": 0, "y": 0, "width": 4000, "height": 3000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 3000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1500, 0, 2000, -1500, 0, -2000, 1500, 0, 2000, 1500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 3000, 4000, 3000, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1500, "z": 0}, "maxPos": {"x": 2000, "y": 1500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [13], [6]], [[[20, "level-2"], [21, "level-2", 33554432, [-21, -22, -23, -24], [[4, -15, [0, "2b0ALzXYtJPo0X8C3Xa/1a"], [5, 5069.977, 3588.3520000000003]], [6, 0, -16, [0, "79uc5hPb9BjJZW5PetmcIM"], 9], [11, -17, [0, "90i9i1N2xGr7Y0i102K+Kd"], [0, -46.3, 1226.9], [5, 3578, 370.9]], [11, -18, [0, "38Ke9wT4JLy6aX4lrvtwau"], [0, -1972.3, 38.1], [5, 420.6, 2177.5]], [11, -19, [0, "dccW0EEldDp6vuUIynuW0Y"], [0, 1912, 17.6], [5, 425.5, 2291.7]], [11, -20, [0, "f6snlE5t5CgLIzIfrs1GWh"], [0, 8.1, -1267.7], [5, 3578, 370.9]]], [26, "48dRzRqnRN2bkSv9uyUwDd", null, -14, 0, [[13, ["healthBar"], -7, -6, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [13, ["healthBar"], -9, -8, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [13, ["healthBar"], -11, -10, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [13, ["healthBar"], -13, -12, [1, ["e9cQsECkREsqrYHJQVVWOE"]]]], [-1, -2, -3, -4, -5]]], [16, "car-1", 2, 33554432, [-30], [[[4, -25, [0, "febEhcVCJCmanY3zuvkWQl"], [5, 27.8, 60]], [14, true, 1, 0, -26, [0, "94TM66uCBAfYenXbYaR3PP"]], [12, 1, -27, [0, "f8cttexglKcaSmhhKAMkZ2"], [5, 27.8, 60.1]], [6, 0, -28, [0, "82rNUMI5tLPa5/vehZCMSl"], 2], -29], 4, 4, 4, 4, 1], [3, "a0U4DNCadE0r1/TONnWKxX", null, null, null, 1, 0], [1, -610.581, 344.475, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [16, "car-2", 2, 33554432, [-36], [[[4, -31, [0, "85OwXYc6FNHpyQKseWyFCb"], [5, 27.8, 60]], [14, true, 1, 0, -32, [0, "98j82JCF1DsaW2LsMjG5dM"]], [12, 1, -33, [0, "00L+B9nGtNa4RJLAvS4V2W"], [5, 27.8, 60.1]], [6, 0, -34, [0, "4cttUISJZABpKvYOz7RLLm"], 4], -35], 4, 4, 4, 4, 1], [3, "70UxTgHRRIgbdZmbJFkqCQ", null, null, null, 1, 0], [1, 609.901, 338.207, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [17, "car-3", 2, 33554432, [-42], [[[4, -37, [0, "558PwHgPFOdKGCb2+HwExW"], [5, 27.8, 60]], [14, true, 1, 0, -38, [0, "13i4+2uKpKr6PqUvnfZePl"]], [12, 1, -39, [0, "549W48hmtGZYtQ/JGQQKsV"], [5, 27.8, 60.1]], [6, 0, -40, [0, "52sVnCxu9GIZUV1v2boA/G"], 6], -41], 4, 4, 4, 4, 1], [3, "7e+qbjVJpPir9JAc3zFrQD", null, null, null, 1, 0], [1, 610.325, -338.674, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, 90]], [17, "car-4", 2, 33554432, [-48], [[[4, -43, [0, "6b2YnnHy1Ddp1gNC/Y5791"], [5, 27.8, 60]], [14, true, 1, 0, -44, [0, "b34bPOC2xDFK2yI4cKdoLt"]], [12, 1, -45, [0, "31wwnhtHBI048/QmNJxKGx"], [5, 27.8, 60.1]], [6, 0, -46, [0, "91vS3IH8tMAIUgXm5i33u2"], 8], -47], 4, 4, 4, 4, 1], [3, "96fHTOmXFAnKNLW7RvoDaM", null, null, null, 1, 0], [1, -610.157, -338.674, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [22, "cars", 33554432, 1, [2, 3, 4, 5], [[18, -49, [0, "1avgl/AfdFt7Y44zEExOVC"]]], [3, "e4K8OMLjZMcaTYEtzbDeUb", null, null, null, 1, 0], [1, 0, -276.598, 0]], [1, ["793wXpE0dMZZF5oHFdhgzy"]], [5, 0, {}, 2, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -54, [8, "f7TetQCR1Hgpj2RNOtPQqo", 1, [[9, "healthBar", ["_name"], -50], [2, ["_lpos"], -51, [1, -35, 0, 0]], [2, ["_lrot"], -52, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -53, [1, 0, 0, 90]], [10, 50, ["offsetY"], [1, ["88S4hp27BPFpL6l7nafPlR"]]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 1]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [5, 0, {}, 3, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -59, [8, "460C7V1GRDkLc9IWZl3Bf6", 1, [[9, "healthBar", ["_name"], -55], [2, ["_lpos"], -56, [1, 35, 0, 0]], [2, ["_lrot"], -57, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -58, [1, 0, 0, 90]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 3]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [5, 0, {}, 4, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -64, [8, "b5EGDwIddFsruBGs4i7y6I", 1, [[9, "healthBar", ["_name"], -60], [2, ["_lpos"], -61, [1, 35, 0, 0]], [2, ["_lrot"], -62, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -63, [1, 0, 0, 90]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 5]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [5, 0, {}, 5, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -69, [8, "ebln8ot9FEbIV/7Ue8Ucuu", 1, [[9, "healthBar", ["_name"], -65], [2, ["_lpos"], -66, [1, -35, 0, 0]], [2, ["_lrot"], -67, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -68, [1, 0, 0, 90]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 7]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [23, "snow", false, 33554432, 1, [[4, -70, [0, "4eV+a/HUtH2pshIXY66OIL"], [5, 5652.656999999999, 3891.1604239999992]], [25, 0, -71, [0, "dcjNsHRcpPfq2v/JQ5kwwg"]]], [3, "73LmHKkq9Nn5nLuc8HvRq9", null, null, null, 1, 0], [1, 79.56, 98.297, 0]], [5, 0, {}, 1, [7, "793wXpE0dMZZF5oHFdhgzy", null, null, -72, [8, "da0Kl7IVBEGIvhTJmMi025", 1, [[9, "PaintRoot", ["_name"], 7], [2, ["_lpos"], 7, [1, -639.97, -359.883, 0]], [2, ["_lrot"], 7, [3, 0, 0, 0, 1]], [2, ["_euler"], 7, [1, 0, 0, 0]]]], 0]], [24, "BulletRoot", 4, 1, [[18, -73, [0, "73OTt9fOxBtblon1GleEvQ"]]], [3, "88dTb61vpDop/QJdLF3Ccr", null, null, null, 1, 0]], [19, 10, 10, 50, 0.1, 2, [0, "c8hi1aIONJQpoPpar0ZX8g"]], [19, 10, 10, 50, 0.1, 3, [0, "beLAG+zO9DqLiZ/wd6PvoZ"]], [27, 10, 10, 80, 0.1, 1, 2, 4, [0, "2aKPnqKLBEXZ/XimfCojso"]], [28, 10, 10, 100, 1, 2, 5, [0, "22t0ZwxIhPK7H4ayFtKFcP"]]], 0, [0, -1, 14, 0, -2, 12, 0, -3, 10, 0, -4, 8, 0, -5, 17, 0, 5, 8, 0, 6, 19, 0, 5, 12, 0, 6, 21, 0, 5, 10, 0, 6, 20, 0, 5, 14, 0, 6, 22, 0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -5, 19, 0, -1, 8, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -5, 20, 0, -1, 10, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -5, 21, 0, -1, 12, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -5, 22, 0, -1, 14, 0, 0, 6, 0, 1, 9, 0, 1, 9, 0, 1, 9, 0, 1, 9, 0, 2, 8, 0, 1, 11, 0, 1, 11, 0, 1, 11, 0, 1, 11, 0, 2, 10, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 2, 12, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 2, 14, 0, 0, 16, 0, 0, 16, 0, 2, 17, 0, 0, 18, 0, 14, 1, 2, 7, 6, 3, 7, 6, 4, 7, 6, 5, 7, 6, 73], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22], [3, 3, 4, 3, 4, 3, 4, 3, 4, 4, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12], [7, 0, 5, 0, 5, 0, 8, 0, 9, 10, 1, 11, 2, 3, 4, 1, 12, 2, 3, 4, 1, 13, 2, 3, 4, 1, 14, 2, 3, 4]]]]