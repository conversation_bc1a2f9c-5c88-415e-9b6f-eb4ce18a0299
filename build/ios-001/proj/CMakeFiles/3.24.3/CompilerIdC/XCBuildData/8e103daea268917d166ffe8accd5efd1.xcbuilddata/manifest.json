{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC": {"is-mutated": true}, "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos": {"is-mutated": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos", "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-scanning>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--end>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--linker-inputs-ready>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--modules-ready>", "<workspace-none--stale-file-removal>"], "outputs": ["<all>"]}, "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-Debug-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist"], "roots": ["/tmp/CompilerIdC.dst", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-Debug-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<workspace-none--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe3-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<workspace-none--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "inputs": [], "outputs": ["/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "<ClangStatCache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "-o", "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj", "signature": "af1fe4ffb0570b43751f1975a65ece9f"}, "P0:::CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC"]}, "P0:::CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos"]}, "P0:::Gate /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM-target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-": {"tool": "phony", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC", "<GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe3-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangePermissions>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-StripSymbols>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangePermissions>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangeAlternatePermissions>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CodeSign>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-GenerateStubAPI>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CopyAside>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductPostprocessingTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CodeSign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-Validate>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<Touch /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-RegisterProduct>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CopyAside>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-StripSymbols>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-Validate": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-RegisterExecutionPolicyException>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-Validate>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--DocumentationTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GeneratedFilesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductStructureTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--RealityAssetsTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--HeadermapTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--InfoPlistTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--HeadermapTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleMapTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleMapTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftPackageCopyFilesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--InfoPlistTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--VersionPlistTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SanitizerTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftStandardLibrariesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftFrameworkABICheckerTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftABIBaselineGenerationTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--StubBinaryTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestTargetTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestHostTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--CopySwiftPackageResourcesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TAPISymbolExtractorTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--DocumentationTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--AppIntentsMetadataTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--start>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<MkDir /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductStructureTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--RealityAssetsTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SanitizerTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--StubBinaryTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestHostTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductPostprocessingTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestTargetTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--VersionPlistTaskProducer>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--copy-headers-completion": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--copy-headers-completion>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase0-compile-sources": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase0-compile-sources>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase0-compile-sources>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<execute-shell-script-d9d24303ac0ea36fa2bc7f98aeea5fe33c78d77b73c835b21897c5edd982f393-target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d->", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--generated-headers": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--generated-headers>"]}, "P0:::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--swift-generated-headers": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--swift-generated-headers>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/CompilerIdC.dst>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-linking": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/CompilerIdC.dst>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-linking>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-scanning": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/CompilerIdC.dst>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-scanning>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--end": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--entry>", "<GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC>", "<GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC>", "<MkDir /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist", "<RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>", "<Touch /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat", "<execute-shell-script-d9d24303ac0ea36fa2bc7f98aeea5fe33c78d77b73c835b21897c5edd982f393-target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d->", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--AppIntentsMetadataTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangeAlternatePermissions>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-ChangePermissions>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CodeSign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CopyAside>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-GenerateStubAPI>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-RegisterExecutionPolicyException>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-RegisterProduct>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-StripSymbols>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-Validate>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--CopySwiftPackageResourcesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--DocumentationTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--GeneratedFilesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--HeadermapTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--InfoPlistTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleMapTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductPostprocessingTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ProductStructureTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--RealityAssetsTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SanitizerTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--StubBinaryTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftABIBaselineGenerationTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftFrameworkABICheckerTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftPackageCopyFilesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--SwiftStandardLibrariesTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TAPISymbolExtractorTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestHostTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestTargetPostprocessingTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--TestTargetTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--VersionPlistTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--copy-headers-completion>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase0-compile-sources>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase1-run-script>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--generated-headers>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--swift-generated-headers>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--end>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--entry": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/CompilerIdC.dst>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--entry>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/CompilerIdC.dst>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC>", "<CreateBuildDirectory-/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--linker-inputs-ready>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--modules-ready": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<execute-shell-script-d9d24303ac0ea36fa2bc7f98aeea5fe33c78d77b73c835b21897c5edd982f393-target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d->", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--modules-ready>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat", "<execute-shell-script-d9d24303ac0ea36fa2bc7f98aeea5fe33c78d77b73c835b21897c5edd982f393-target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d->", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-GenerateStubAPI>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--unsigned-product-ready>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Gate target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign": {"tool": "phony", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--unsigned-product-ready>"], "outputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist", "<Linked Binary /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC", "<GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM/Contents/Resources/DWARF/CompilerIdC>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC", "-o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM"], "env": {}, "working-directory": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "signature": "67d727cf1e8235f3f81643b87db5d291"}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::MkDir /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest": {"tool": "mkdir", "description": "MkDir /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--start>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "<MkDir /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::ProcessInfoPlistFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--entry>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-CodeSign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>"]}, "P0:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Touch /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest": {"tool": "shell", "description": "Touch /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--Barrier-Validate>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--will-sign>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--entry>"], "outputs": ["<Touch /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest"], "env": {}, "working-directory": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "signature": "8c8db0430f9fde5c769df39839ca75ea"}, "P1:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CMakeCCompilerId.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "shell", "description": "CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CMakeCCompilerId.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CMakeCCompilerId.c", "<ClangStatCache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--generated-headers>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--swift-generated-headers>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c", "-ivfsstatcache", "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Wno-return-type", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wno-unused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wno-unused-variable", "-Wunused-value", "-Wno-empty-body", "-Wno-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wno-constant-conversion", "-Wno-int-conversion", "-Wno-bool-conversion", "-Wno-enum-conversion", "-Wno-float-conversion", "-Wno-non-literal-null-conversion", "-Wno-objc-literal-conversion", "-Wshorten-64-to-32", "-Wpointer-sign", "-Wno-newline-eof", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Wno-sign-conversion", "-Wno-infinite-recursion", "-Wno-comma", "-Wno-block-capture-autoreleasing", "-Wno-strict-prototypes", "-Wno-semicolon-before-method-body", "@/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.d", "--serialize-diagnostics", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.dia", "-c", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CMakeCCompilerId.c", "-o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o"], "env": {}, "working-directory": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "deps": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.d"], "deps-style": "makefile", "signature": "9db3d52515fb9212e17a032bb5988ce2"}, "P2:::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe3-VFS-iphoneos/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe3-VFS-iphoneos/all-product-headers.yaml", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe3-VFS-iphoneos/all-product-headers.yaml"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::Ld /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC normal": {"tool": "shell", "description": "Ld /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC normal", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--generated-headers>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--swift-generated-headers>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--ModuleVerifierTaskProducer>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--begin-linking>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC", "<Linked Binary /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2", "-bundle", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos", "-L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "-L/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos", "-F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "-iframework", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-iframework", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks", "-filelist", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@loader_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o", "-framework", "XCTest", "-lXCTestSwiftSupport", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat", "-o", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC"], "env": {}, "working-directory": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "deps": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat"], "deps-style": "dependency-info", "signature": "13b49f02bee98ba63388872c555c493d"}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::PhaseScriptExecution Run Script /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh": {"tool": "shell", "description": "PhaseScriptExecution Run Script /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh", "inputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--fused-phase0-compile-sources>", "<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--entry>"], "outputs": ["<execute-shell-script-d9d24303ac0ea36fa2bc7f98aeea5fe33c78d77b73c835b21897c5edd982f393-target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d->"], "args": ["/bin/sh", "-c", "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh"], "env": {"ACTION": "build", "AD_HOC_CODE_SIGNING_ALLOWED": "NO", "ALLOW_BUILD_REQUEST_OVERRIDES": "NO", "ALLOW_TARGET_PLATFORM_SPECIALIZATION": "NO", "ALTERNATE_GROUP": "staff", "ALTERNATE_MODE": "u+w,go-w,a+rX", "ALTERNATE_OWNER": "<PERSON><PERSON><PERSON><PERSON>", "ALTERNATIVE_DISTRIBUTION_WEB": "NO", "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "ALWAYS_SEARCH_USER_PATHS": "YES", "ALWAYS_USE_SEPARATE_HEADERMAPS": "NO", "APPLE_INTERNAL_DEVELOPER_DIR": "/AppleInternal/Developer", "APPLE_INTERNAL_DIR": "/AppleInternal", "APPLE_INTERNAL_DOCUMENTATION_DIR": "/AppleInternal/Documentation", "APPLE_INTERNAL_LIBRARY_DIR": "/AppleInternal/Library", "APPLE_INTERNAL_TOOLS": "/AppleInternal/Developer/Tools", "APPLICATION_EXTENSION_API_ONLY": "NO", "APPLY_RULES_IN_COPY_FILES": "NO", "APPLY_RULES_IN_COPY_HEADERS": "NO", "APP_SHORTCUTS_ENABLE_FLEXIBLE_MATCHING": "YES", "ARCHS": "arm64", "ARCHS_STANDARD": "arm64", "ARCHS_STANDARD_32_64_BIT": "armv7 arm64", "ARCHS_STANDARD_32_BIT": "armv7", "ARCHS_STANDARD_64_BIT": "arm64", "ARCHS_STANDARD_INCLUDING_64_BIT": "arm64", "ARCHS_UNIVERSAL_IPHONE_OS": "armv7 arm64", "ASSETCATALOG_COMPILER_GENERATE_ASSET_SYMBOLS": "YES", "AUTOMATICALLY_MERGE_DEPENDENCIES": "NO", "AVAILABLE_PLATFORMS": "appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator xros xrsimulator", "BITCODE_GENERATION_MODE": "marker", "BUILD_ACTIVE_RESOURCES_ONLY": "NO", "BUILD_COMPONENTS": "headers build", "BUILD_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "BUILD_ROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "BUILD_STYLE": "", "BUILD_VARIANTS": "normal", "BUILT_PRODUCTS_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "BUNDLE_CONTENTS_FOLDER_PATH_deep": "Contents/", "BUNDLE_EXECUTABLE_FOLDER_NAME_deep": "MacOS", "BUNDLE_EXTENSIONS_FOLDER_PATH": "Extensions", "BUNDLE_FORMAT": "shallow", "BUNDLE_FRAMEWORKS_FOLDER_PATH": "Frameworks", "BUNDLE_PLUGINS_FOLDER_PATH": "PlugIns", "BUNDLE_PRIVATE_HEADERS_FOLDER_PATH": "PrivateHeaders", "BUNDLE_PUBLIC_HEADERS_FOLDER_PATH": "Headers", "CACHE_ROOT": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode", "CCHROOT": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode", "CHMOD": "/bin/chmod", "CHOWN": "/usr/sbin/chown", "CLANG_CACHE_FINE_GRAINED_OUTPUTS": "YES", "CLANG_ENABLE_EXPLICIT_MODULES": "YES", "CLANG_MODULES_BUILD_SESSION_FILE": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/org.llvm.clang/ModuleCache.noindex/Session.modulevalidation", "CLASS_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/JavaClasses", "CLEAN_PRECOMPS": "YES", "CLONE_HEADERS": "NO", "CODESIGNING_FOLDER_PATH": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "CODE_SIGNING_ALLOWED": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_CONTEXT_CLASS": "XCiPhoneOSCodeSignContext", "CODE_SIGN_INJECT_BASE_ENTITLEMENTS": "YES", "COLOR_DIAGNOSTICS": "NO", "COMBINE_HIDPI_IMAGES": "NO", "COMPILATION_CACHE_CAS_PATH": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/CompilationCache.noindex", "COMPILATION_CACHE_KEEP_CAS_DIRECTORY": "YES", "COMPILER_INDEX_STORE_ENABLE": "<PERSON><PERSON><PERSON>", "COMPOSITE_SDK_DIRS": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompositeSDKs", "COMPRESS_PNG_FILES": "YES", "CONFIGURATION": "Debug", "CONFIGURATION_BUILD_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "CONFIGURATION_TEMP_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos", "CONTENTS_FOLDER_PATH": "CompilerIdC.xctest", "CONTENTS_FOLDER_PATH_SHALLOW_BUNDLE_NO": "CompilerIdC.xctest/Contents", "CONTENTS_FOLDER_PATH_SHALLOW_BUNDLE_YES": "CompilerIdC.xctest", "COPYING_PRESERVES_HFS_DATA": "NO", "COPY_HEADERS_RUN_UNIFDEF": "NO", "COPY_PHASE_STRIP": "YES", "CORRESPONDING_SIMULATOR_PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform", "CORRESPONDING_SIMULATOR_PLATFORM_NAME": "iphonesimulator", "CORRESPONDING_SIMULATOR_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "CORRESPONDING_SIMULATOR_SDK_NAME": "iphonesimulator18.2", "CP": "/bin/cp", "CREATE_INFOPLIST_SECTION_IN_BINARY": "NO", "CURRENT_ARCH": "undefined_arch", "CURRENT_VARIANT": "normal", "DEAD_CODE_STRIPPING": "YES", "DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEBUG_INFORMATION_VERSION": "compiler-default", "DEFAULT_COMPILER": "com.apple.compilers.llvm.clang.1_0", "DEFAULT_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "DEFAULT_KEXT_INSTALL_PATH": "/System/Library/Extensions", "DEFINES_MODULE": "NO", "DEPLOYMENT_LOCATION": "NO", "DEPLOYMENT_POSTPROCESSING": "NO", "DEPLOYMENT_TARGET_SETTING_NAME": "IPHONEOS_DEPLOYMENT_TARGET", "DEPLOYMENT_TARGET_SUGGESTED_VALUES": "12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0 15.1 15.2 15.3 15.4 15.5 15.6 16.0 16.1 16.2 16.3 16.4 16.5 16.6 17.0 17.1 17.2 17.3 17.4 17.5 17.6 18.0 18.1 18.2", "DERIVED_FILES_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/DerivedSources", "DERIVED_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/DerivedSources", "DERIVED_SOURCES_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/DerivedSources", "DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER": "NO", "DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "DEVELOPER_FRAMEWORKS_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_FRAMEWORKS_DIR_QUOTED": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "DEVELOPMENT_LANGUAGE": "English", "DIFF": "/usr/bin/diff", "DOCUMENTATION_FOLDER_PATH": "CompilerIdC.xctest/English.lproj/Documentation", "DONT_GENERATE_INFOPLIST_FILE": "NO", "DSTROOT": "/tmp/CompilerIdC.dst", "DT_TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "DWARF_DSYM_FILE_NAME": "CompilerIdC.xctest.dSYM", "DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT": "NO", "DWARF_DSYM_FOLDER_PATH": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "DYNAMIC_LIBRARY_EXTENSION": "dylib", "EAGER_COMPILATION_ALLOW_SCRIPTS": "NO", "EAGER_LINKING": "NO", "EFFECTIVE_PLATFORM_NAME": "-iphoneos", "EMBEDDED_CONTENT_CONTAINS_SWIFT": "NO", "EMBEDDED_PROFILE_NAME": "embedded.mobileprovision", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_APP_SANDBOX": "NO", "ENABLE_BITCODE": "NO", "ENABLE_CODE_COVERAGE": "YES", "ENABLE_DEFAULT_HEADER_SEARCH_PATHS": "YES", "ENABLE_DEFAULT_SEARCH_PATHS": "YES", "ENABLE_HARDENED_RUNTIME": "NO", "ENABLE_HEADER_DEPENDENCIES": "YES", "ENABLE_ON_DEMAND_RESOURCES": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_TESTABILITY": "NO", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "ENTITLEMENTS_DESTINATION": "Signature", "ENTITLEMENTS_REQUIRED": "NO", "EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS": ".DS_Store .svn .git .hg CVS", "EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES": "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj", "EXECUTABLES_FOLDER_PATH": "CompilerIdC.xctest/Executables", "EXECUTABLE_FOLDER_PATH": "CompilerIdC.xctest", "EXECUTABLE_FOLDER_PATH_SHALLOW_BUNDLE_NO": "CompilerIdC.xctest/MacOS", "EXECUTABLE_FOLDER_PATH_SHALLOW_BUNDLE_YES": "CompilerIdC.xctest", "EXECUTABLE_NAME": "CompilerIdC", "EXECUTABLE_PATH": "CompilerIdC.xctest/CompilerIdC", "EXTENSIONS_FOLDER_PATH": "CompilerIdC.xctest/Extensions", "FILE_LIST": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects/LinkFileList", "FIXED_FILES_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/FixedFiles", "FRAMEWORKS_FOLDER_PATH": "CompilerIdC.xctest/Frameworks", "FRAMEWORK_FLAG_PREFIX": "-framework", "FRAMEWORK_SEARCH_PATHS": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC ", "FRAMEWORK_VERSION": "A", "FULL_PRODUCT_NAME": "CompilerIdC.xctest", "FUSE_BUILD_PHASES": "YES", "FUSE_BUILD_SCRIPT_PHASES": "NO", "GCC3_VERSION": "3.3", "GCC_INLINES_ARE_PRIVATE_EXTERN": "YES", "GCC_PFE_FILE_C_DIALECTS": "c objective-c c++ objective-c++", "GCC_THUMB_SUPPORT": "YES", "GCC_TREAT_WARNINGS_AS_ERRORS": "NO", "GCC_VERSION": "com.apple.compilers.llvm.clang.1_0", "GCC_VERSION_IDENTIFIER": "com_apple_compilers_llvm_clang_1_0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GENERATED_MODULEMAP_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/GeneratedModuleMaps-iphoneos", "GENERATE_INFOPLIST_FILE": "YES", "GENERATE_INTERMEDIATE_TEXT_BASED_STUBS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_PKGINFO_FILE": "NO", "GENERATE_PROFILING_CODE": "NO", "GENERATE_TEXT_BASED_STUBS": "NO", "GID": "20", "GROUP": "staff", "HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_TARGETS_NOT_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS": "YES", "HEADERMAP_INCLUDES_PROJECT_HEADERS": "YES", "HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES": "YES", "HEADERMAP_USES_VFS": "NO", "HEADER_SEARCH_PATHS": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/include ", "HIDE_BITCODE_SYMBOLS": "YES", "HOME": "/Users/<USER>", "HOST_ARCH": "arm64", "HOST_PLATFORM": "macosx", "ICONV": "/usr/bin/iconv", "IMPLICIT_DEPENDENCY_DOMAIN": "default", "INFOPLIST_ENABLE_CFBUNDLEICONS_MERGE": "YES", "INFOPLIST_EXPAND_BUILD_SETTINGS": "YES", "INFOPLIST_OUTPUT_FORMAT": "binary", "INFOPLIST_PATH": "CompilerIdC.xctest/Info.plist", "INFOPLIST_PREPROCESS": "NO", "INFOSTRINGS_PATH": "CompilerIdC.xctest/English.lproj/InfoPlist.strings", "INLINE_PRIVATE_FRAMEWORKS": "NO", "INSTALLHDRS_COPY_PHASE": "NO", "INSTALLHDRS_SCRIPT_PHASE": "NO", "INSTALL_DIR": "/tmp/CompilerIdC.dst", "INSTALL_GROUP": "staff", "INSTALL_MODE_FLAG": "u+w,go-w,a+rX", "INSTALL_OWNER": "<PERSON><PERSON><PERSON><PERSON>", "INSTALL_ROOT": "/tmp/CompilerIdC.dst", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "IS_UNOPTIMIZED_BUILD": "NO", "JAVAC_DEFAULT_FLAGS": "-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8", "JAVA_APP_STUB": "/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub", "JAVA_ARCHIVE_CLASSES": "YES", "JAVA_ARCHIVE_TYPE": "JAR", "JAVA_COMPILER": "/usr/bin/javac", "JAVA_FOLDER_PATH": "CompilerIdC.xctest/Java", "JAVA_FRAMEWORK_RESOURCES_DIRS": "Resources", "JAVA_JAR_FLAGS": "cv", "JAVA_SOURCE_SUBDIR": ".", "JAVA_USE_DEPENDENCIES": "YES", "JAVA_ZIP_FLAGS": "-urg", "JIKES_DEFAULT_FLAGS": "+E +OLDCSO", "KASAN_CFLAGS_CLASSIC": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KASAN_CFLAGS_TBI": "-DKASAN=1 -DKASAN_TBI=1 -fsanitize=kernel-hwaddress -mllvm -hwasan-recover=0 -mllvm -hwasan-instrument-atomics=0 -mllvm -hwasan-instrument-stack=1 -mllvm -hwasan-generate-tags-with-calls=1 -mllvm -hwasan-instrument-with-calls=1 -mllvm -hwasan-use-short-granules=0 -mllvm -hwasan-memory-access-callback-prefix=__asan_", "KASAN_DEFAULT_CFLAGS": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KEEP_PRIVATE_EXTERNS": "NO", "LD_DEPENDENCY_INFO_FILE": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/undefined_arch/CompilerIdC_dependency_info.dat", "LD_EXPORT_SYMBOLS": "YES", "LD_GENERATE_MAP_FILE": "NO", "LD_MAP_FILE_PATH": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-LinkMap-normal-undefined_arch.txt", "LD_NO_PIE": "NO", "LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER": "YES", "LD_RUNPATH_SEARCH_PATHS": " @loader_path/Frameworks", "LD_RUNPATH_SEARCH_PATHS_SHALLOW_BUNDLE_NO": "@loader_path/../Frameworks", "LD_RUNPATH_SEARCH_PATHS_SHALLOW_BUNDLE_YES": "@loader_path/Frameworks", "LD_SHARED_CACHE_ELIGIBLE": "Automatic", "LD_WARN_DUPLICATE_LIBRARIES": "NO", "LD_WARN_UNUSED_DYLIBS": "NO", "LEGACY_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer", "LEX": "lex", "LIBRARY_DEXT_INSTALL_PATH": "/Library/DriverExtensions", "LIBRARY_FLAG_NOSPACE": "YES", "LIBRARY_FLAG_PREFIX": "-l", "LIBRARY_KEXT_INSTALL_PATH": "/Library/Extensions", "LIBRARY_SEARCH_PATHS": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC   /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "LINKER_DISPLAYS_MANGLED_NAMES": "NO", "LINK_FILE_LIST_normal_arm64": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList", "LINK_OBJC_RUNTIME": "YES", "LINK_WITH_STANDARD_LIBRARIES": "YES", "LLVM_TARGET_TRIPLE_OS_VERSION": "ios18.2", "LLVM_TARGET_TRIPLE_VENDOR": "apple", "LM_AUX_CONST_METADATA_LIST_PATH_normal_arm64": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.SwiftConstValuesFileList", "LOCALIZATION_EXPORT_SUPPORTED": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "NO", "LOCALIZED_RESOURCES_FOLDER_PATH": "CompilerIdC.xctest/English.lproj", "LOCALIZED_STRING_MACRO_NAMES": "NSLocalizedString CFCopyLocalizedString", "LOCALIZED_STRING_SWIFTUI_SUPPORT": "YES", "LOCAL_ADMIN_APPS_DIR": "/Applications/Utilities", "LOCAL_APPS_DIR": "/Applications", "LOCAL_DEVELOPER_DIR": "/Library/Developer", "LOCAL_LIBRARY_DIR": "/Library", "LOCROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "LOCSYMROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "MACH_O_TYPE": "mh_bundle", "MAC_OS_X_PRODUCT_BUILD_VERSION": "24G84", "MAC_OS_X_VERSION_ACTUAL": "150600", "MAC_OS_X_VERSION_MAJOR": "150000", "MAC_OS_X_VERSION_MINOR": "150600", "MAKE_MERGEABLE": "NO", "MERGEABLE_LIBRARY": "NO", "MERGED_BINARY_TYPE": "none", "MERGE_LINKED_LIBRARIES": "NO", "METAL_LIBRARY_FILE_BASE": "default", "METAL_LIBRARY_OUTPUT_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest", "MODULES_FOLDER_PATH": "CompilerIdC.xctest/Modules", "NATIVE_ARCH": "arm64", "NATIVE_ARCH_32_BIT": "arm", "NATIVE_ARCH_64_BIT": "arm64", "NATIVE_ARCH_ACTUAL": "arm64", "NO_COMMON": "YES", "OBJECT_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects", "OBJECT_FILE_DIR_normal": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal", "OBJROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "ONLY_ACTIVE_ARCH": "YES", "OS": "MACOS", "OSAC": "/usr/bin/osacompile", "PACKAGE_TYPE": "com.apple.package-type.bundle.unit-test", "PASCAL_STRINGS": "YES", "PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/XCBuild.framework/Versions/A/PlugIns/XCBBuildService.bundle/Contents/PlugIns/XCBSpecifications.ideplugin/Contents/Resources:/Applications/Xcode.app/Contents/SharedFrameworks/XCBuild.framework/Versions/A/PlugIns/XCBBuildService.bundle/Contents/PlugIns/XCBSpecifications.ideplugin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/appleinternal/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/appleinternal/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/opt/homebrew/Cellar/node/24.5.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/node_modules/.bin:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS:/Applications/CocosDashboard.app/Contents/MacOS", "PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES": "/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms", "PBDEVELOPMENTPLIST_PATH": "CompilerIdC.xctest/pbdevelopment.plist", "PER_ARCH_OBJECT_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/undefined_arch", "PER_VARIANT_OBJECT_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal", "PKGINFO_FILE_PATH": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/PkgInfo", "PKGINFO_PATH": "CompilerIdC.xctest/PkgInfo", "PLATFORM_DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications", "PLATFORM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin", "PLATFORM_DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library", "PLATFORM_DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs", "PLATFORM_DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools", "PLATFORM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr", "PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform", "PLATFORM_DISPLAY_NAME": "iOS", "PLATFORM_FAMILY_NAME": "iOS", "PLATFORM_NAME": "iphoneos", "PLATFORM_PREFERRED_ARCH": "arm64", "PLATFORM_PRODUCT_BUILD_VERSION": "22C146", "PLIST_FILE_OUTPUT_FORMAT": "binary", "PLUGINS_FOLDER_PATH": "CompilerIdC.xctest/PlugIns", "PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR": "YES", "PRECOMP_DESTINATION_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/PrefixHeaders", "PRIVATE_HEADERS_FOLDER_PATH": "CompilerIdC.xctest/PrivateHeaders", "PROCESSED_INFOPLIST_PATH": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/undefined_arch/Processed-Info.plist", "PRODUCT_BUNDLE_PACKAGE_TYPE": "BNDL", "PRODUCT_MODULE_NAME": "CompilerIdC", "PRODUCT_NAME": "CompilerIdC", "PRODUCT_SETTINGS_PATH": "", "PRODUCT_SPECIFIC_LDFLAGS": "-framework XCTest -lXCTestSwiftSupport", "PRODUCT_TYPE": "com.apple.product-type.bundle.unit-test", "PRODUCT_TYPE_SWIFT_STDLIB_TOOL_FLAGS": "--scan-executable \"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/libXCTestSwiftSupport.dylib\"", "PROFILING_CODE": "NO", "PROJECT": "CompilerIdC", "PROJECT_DERIVED_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/DerivedSources", "PROJECT_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "PROJECT_FILE_PATH": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj", "PROJECT_GUID": "d9d24303ac0ea36fa2bc7f98aeea5fe3", "PROJECT_NAME": "CompilerIdC", "PROJECT_TEMP_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build", "PROJECT_TEMP_ROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "PROVISIONING_PROFILE_REQUIRED": "NO", "PROVISIONING_PROFILE_SUPPORTED": "YES", "PUBLIC_HEADERS_FOLDER_PATH": "CompilerIdC.xctest/Headers", "RECOMMENDED_IPHONEOS_DEPLOYMENT_TARGET": "15.0", "RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS": "YES", "REMOVE_CVS_FROM_RESOURCES": "YES", "REMOVE_GIT_FROM_RESOURCES": "YES", "REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_HG_FROM_RESOURCES": "YES", "REMOVE_STATIC_EXECUTABLES_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_SVN_FROM_RESOURCES": "YES", "RESCHEDULE_INDEPENDENT_HEADERS_PHASES": "YES", "REZ_COLLECTOR_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/ResourceManagerResources", "REZ_OBJECTS_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/ResourceManagerResources/Objects", "REZ_SEARCH_PATHS": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC ", "SCAN_ALL_SOURCE_FILES_FOR_INCLUDES": "NO", "SCRIPTS_FOLDER_PATH": "CompilerIdC.xctest/Scripts", "SCRIPT_INPUT_FILE_COUNT": "0", "SCRIPT_INPUT_FILE_LIST_COUNT": "0", "SCRIPT_OUTPUT_FILE_COUNT": "0", "SCRIPT_OUTPUT_FILE_LIST_COUNT": "0", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "SDK_DIR_iphoneos": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "SDK_DIR_iphoneos18_2": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "SDK_NAME": "iphoneos18.2", "SDK_NAMES": "iphoneos18.2", "SDK_PRODUCT_BUILD_VERSION": "22C146", "SDK_STAT_CACHE_DIR": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode", "SDK_STAT_CACHE_ENABLE": "YES", "SDK_STAT_CACHE_PATH": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "SDK_VERSION": "18.2", "SDK_VERSION_ACTUAL": "180200", "SDK_VERSION_MAJOR": "180000", "SDK_VERSION_MINOR": "180200", "SED": "/usr/bin/sed", "SEPARATE_STRIP": "NO", "SEPARATE_SYMBOL_EDIT": "NO", "SET_DIR_MODE_OWNER_GROUP": "YES", "SET_FILE_MODE_OWNER_GROUP": "NO", "SHALLOW_BUNDLE": "YES", "SHALLOW_BUNDLE_TRIPLE": "ios", "SHALLOW_BUNDLE_ios_macabi": "NO", "SHALLOW_BUNDLE_macos": "NO", "SHARED_DERIVED_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/DerivedSources", "SHARED_FRAMEWORKS_FOLDER_PATH": "CompilerIdC.xctest/SharedFrameworks", "SHARED_PRECOMPS_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/SharedPrecompiledHeaders", "SHARED_SUPPORT_FOLDER_PATH": "CompilerIdC.xctest/SharedSupport", "SKIP_INSTALL": "YES", "SOURCE_ROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "SRCROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "STRINGSDATA_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/undefined_arch", "STRINGSDATA_ROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build", "STRINGS_FILE_INFOPLIST_RENAME": "YES", "STRINGS_FILE_OUTPUT_ENCODING": "binary", "STRIP_BITCODE_FROM_COPIED_FILES": "YES", "STRIP_INSTALLED_PRODUCT": "YES", "STRIP_STYLE": "non-global", "STRIP_SWIFT_SYMBOLS": "YES", "SUPPORTED_DEVICE_FAMILIES": "1,2", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator", "SUPPORTS_MACCATALYST": "YES", "SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD": "YES", "SUPPORTS_TEXT_BASED_API": "NO", "SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD": "YES", "SUPPRESS_WARNINGS": "NO", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_PLATFORM_TARGET_PREFIX": "ios", "SWIFT_RESPONSE_FILE_PATH_normal_arm64": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.SwiftFileList", "SYMROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "SYSTEM_ADMIN_APPS_DIR": "/Applications/Utilities", "SYSTEM_APPS_DIR": "/Applications", "SYSTEM_CORE_SERVICES_DIR": "/System/Library/CoreServices", "SYSTEM_DEMOS_DIR": "/Applications/Extras", "SYSTEM_DEVELOPER_APPS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "SYSTEM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "SYSTEM_DEVELOPER_DEMOS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples", "SYSTEM_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SYSTEM_DEVELOPER_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library", "SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools", "SYSTEM_DEVELOPER_JAVA_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Java Tools", "SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools", "SYSTEM_DEVELOPER_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes", "SYSTEM_DEVELOPER_TOOLS": "/Applications/Xcode.app/Contents/Developer/Tools", "SYSTEM_DEVELOPER_TOOLS_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools", "SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools", "SYSTEM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "SYSTEM_DEVELOPER_UTILITIES_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities", "SYSTEM_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "SYSTEM_DOCUMENTATION_DIR": "/Library/Documentation", "SYSTEM_EXTENSIONS_FOLDER_PATH": "CompilerIdC.xctest/SystemExtensions", "SYSTEM_EXTENSIONS_FOLDER_PATH_SHALLOW_BUNDLE_NO": "CompilerIdC.xctest/Library/SystemExtensions", "SYSTEM_EXTENSIONS_FOLDER_PATH_SHALLOW_BUNDLE_YES": "CompilerIdC.xctest/SystemExtensions", "SYSTEM_FRAMEWORK_SEARCH_PATHS": "  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks", "SYSTEM_KEXT_INSTALL_PATH": "/System/Library/Extensions", "SYSTEM_LIBRARY_DIR": "/System/Library", "TAPI_DEMANGLE": "YES", "TAPI_ENABLE_PROJECT_HEADERS": "NO", "TAPI_LANGUAGE": "objective-c", "TAPI_LANGUAGE_STANDARD": "compiler-default", "TAPI_USE_SRCROOT": "YES", "TAPI_VERIFY_MODE": "Pedantic", "TARGETED_DEVICE_FAMILY": "1", "TARGETNAME": "CompilerIdC", "TARGET_BUILD_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "TARGET_NAME": "CompilerIdC", "TARGET_TEMP_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build", "TEMP_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build", "TEMP_FILES_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build", "TEMP_FILE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build", "TEMP_ROOT": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "TEST_FRAMEWORK_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks", "TEST_LIBRARY_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "TOOLCHAINS": "com.apple.dt.toolchain.XcodeDefault", "TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "TREAT_MISSING_BASELINES_AS_TEST_FAILURES": "NO", "TREAT_MISSING_SCRIPT_PHASE_OUTPUTS_AS_ERRORS": "NO", "UID": "501", "UNINSTALLED_PRODUCTS_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/UninstalledProducts", "UNLOCALIZED_RESOURCES_FOLDER_PATH": "CompilerIdC.xctest", "UNLOCALIZED_RESOURCES_FOLDER_PATH_SHALLOW_BUNDLE_NO": "CompilerIdC.xctest/Resources", "UNLOCALIZED_RESOURCES_FOLDER_PATH_SHALLOW_BUNDLE_YES": "CompilerIdC.xctest", "UNSTRIPPED_PRODUCT": "NO", "USER": "<PERSON><PERSON><PERSON><PERSON>", "USER_APPS_DIR": "/Users/<USER>/Applications", "USER_LIBRARY_DIR": "/Users/<USER>/Library", "USE_DYNAMIC_NO_PIC": "YES", "USE_HEADERMAP": "YES", "USE_HEADER_SYMLINKS": "NO", "VALIDATE_DEVELOPMENT_ASSET_PATHS": "YES_ERROR", "VALIDATE_PRODUCT": "NO", "VALID_ARCHS": "arm64 arm64e armv7 armv7s", "VERBOSE_PBXCP": "NO", "VERSIONPLIST_PATH": "CompilerIdC.xctest/version.plist", "VERSION_INFO_BUILDER": "<PERSON><PERSON><PERSON><PERSON>", "VERSION_INFO_FILE": "CompilerIdC_vers.c", "VERSION_INFO_STRING": "\"@(#)PROGRAM:CompilerIdC  PROJECT:CompilerIdC-\"", "WORKSPACE_DIR": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj", "WRAPPER_EXTENSION": "xctest", "WRAPPER_NAME": "CompilerIdC.xctest", "WRAPPER_SUFFIX": ".xctest", "WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES": "NO", "XCODE_APP_SUPPORT_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Xcode", "XCODE_PRODUCT_BUILD_VERSION": "16C5032a", "XCODE_VERSION_ACTUAL": "1620", "XCODE_VERSION_MAJOR": "1600", "XCODE_VERSION_MINOR": "1620", "XPCSERVICES_FOLDER_PATH": "CompilerIdC.xctest/XPCServices", "YACC": "yacc", "_WRAPPER_CONTENTS_DIR_SHALLOW_BUNDLE_NO": "/Contents", "_WRAPPER_PARENT_PATH_SHALLOW_BUNDLE_NO": "/..", "_WRAPPER_RESOURCES_DIR_SHALLOW_BUNDLE_NO": "/Resources", "arch": "undefined_arch", "variant": "normal"}, "allow-missing-inputs": true, "always-out-of-date": true, "working-directory": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC", "control-enabled": false, "repair-via-ownership-analysis": true, "signature": "ff09518762e365e814d059d06b2e5bc7"}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh"]}, "P2:target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d-::WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist", "inputs": ["<target-CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d--immediate>"], "outputs": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist"]}}}