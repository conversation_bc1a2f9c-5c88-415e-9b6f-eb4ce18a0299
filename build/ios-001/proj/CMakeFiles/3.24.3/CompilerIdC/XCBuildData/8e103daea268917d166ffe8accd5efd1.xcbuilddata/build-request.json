{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "d9d24303ac0ea36fa2bc7f98aeea5fe33f938c03aee3f24af46a5caa592e362d"}], "containerPath": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios-001/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO"}}}}, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": false}