[1, ["5fpKAgrRJNUZ59V87NwFbK", "86CHtLQoRLkYcH+h9xQG6h@f9941", "ff3ACRArVH6Zy6dSrQ9/Jg", "525Wn55e9KfJgHyymrwV2G", "7f46LBRONF7aOkgekKaZTH", "4942ZUssNH2a/rXbKChqGw@f9941", "4dUynpj2dD46OZ4PqaWtX0", "b96V8CSuxMAYNDHGzgNrDG@f9941", "74VPnxR5ZAWr1nzOODXOSh@f9941", "b7HBnQSslNy6oytl/eLEBq", "a4Yof/90tAdaSDCLECf+ct", "35QYurHgpBCKJnzoyMJCLD", "46ZtbII3ZN9L4o1m44O6rO", "74VPnxR5ZAWr1nzOODXOSh@6c48a"], ["node", "targetInfo", "target", "root", "asset", "_spriteFrame", "source", "_parent", "destroyedSprite", "paintPrefab", "normalBulletPrefab", "dartPrefab", "rocketPrefab", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children"], -1, 4, 9, 1, 2], ["cc.Node", ["_name", "_mobility", "_layer", "_children", "_components", "_prefab", "_lpos", "_lrot", "_euler", "_lscale"], 0, 2, 12, 4, 5, 5, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.BoxCollider2D", ["_friction", "node", "__prefab", "_size", "_offset"], 2, 1, 4, 5, 5], ["cc.TargetOverrideInfo", ["propertyPath", "target", "targetInfo", "source"], 2, 1, 4, 1], ["91c082Rgh1HBbV2vKQl2J1W", ["maxSpeed", "acceleration", "maxHealth", "paintSprayInterval", "fireRate", "weaponType", "node", "__prefab"], -3, 1, 4], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.RigidBody2D", ["enabledContactListener", "_type", "_gravityScale", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8]], [[8, 0, 2], [13, 0, 2], [17, 0, 1, 2, 2], [19, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 5], [18, 0, 1, 2, 3], [0, 2, 3, 6, 4, 3], [2, 0, 1, 2, 1], [9, 0, 1, 2, 3, 2], [12, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 2], [3, 1, 2, 4, 3, 1], [3, 0, 1, 2, 3, 2], [4, 0, 1, 2, 2], [4, 0, 3, 1, 2, 2], [14, 0, 1, 2, 3, 4, 4], [16, 0, 1, 2, 3], [1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 4], [5, 0, 1, 2, 3, 4, 6, 7, 6], [2, 0, 1, 1], [7, 0, 2], [0, 0, 1, 7, 5, 4, 3], [0, 0, 1, 6, 7, 5, 4, 3], [0, 0, 1, 6, 5, 4, 3], [1, 0, 1, 2, 3, 4, 5, 6, 7, 9, 8, 4], [10, 0, 1, 2, 3, 4, 5, 3], [5, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[[20, "level-1"], [21, "level-1", 33554432, [-25, -26, -27], [[7, -19, [0, "2b0ALzXYtJPo0X8C3Xa/1a"], [5, 4914.686, 3555.9869999999996]], [8, 0, -20, [0, "79uc5hPb9BjJZW5PetmcIM"], 9], [11, -21, [0, "7cyAmiMBRE+6g7LbrLDhAi"], [0, -1.3, 1080.4], [5, 3380.2, 468.9]], [11, -22, [0, "90i9i1N2xGr7Y0i102K+Kd"], [0, -0.1, -1206.8], [5, 3378, 295.7]], [11, -23, [0, "38Ke9wT4JLy6aX4lrvtwau"], [0, -1746, 0], [5, 569.6, 2105.4]], [11, -24, [0, "dccW0EEldDp6vuUIynuW0Y"], [0, 1720, 0.3], [5, 575.1, 2105.4]]], [25, "48dRzRqnRN2bkSv9uyUwDd", null, -18, 0, [[13, ["healthBar"], -6, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [13, ["healthBar"], -7, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [13, ["healthBar"], -8, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [13, ["healthBar"], -9, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [14, ["healthBar"], -11, -10, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [14, ["healthBar"], -13, -12, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [14, ["healthBar"], -15, -14, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [14, ["healthBar"], -17, -16, [1, ["e9cQsECkREsqrYHJQVVWOE"]]]], [-1, -2, -3, -4, -5]]], [17, "car-1", 2, 33554432, [-33], [[[7, -28, [0, "f8ivpgVn1OJ5e7cGl6aaYe"], [5, 27.8, 60]], [15, true, 1, 0, -29, [0, "dc4jNeVqROo6/3hGgBnvkK"]], [12, 1, -30, [0, "89hm8UkkZOip/wqw9sFI4r"], [5, 27.8, 60.1]], [8, 0, -31, [0, "18Z2XGRaJAXav3gxfJD9K0"], 2], -32], 4, 4, 4, 4, 1], [4, "666rSHmv1M3oh+vr+GTk4F", null, null, null, 1, 0], [1, -610.581, 344.475, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [17, "car-2", 2, 33554432, [-39], [[[7, -34, [0, "cffzrHCSdEJ6NvU5Qxg+id"], [5, 27.8, 60]], [15, true, 1, 0, -35, [0, "20i69dGXVCmYeTl9YhdbKh"]], [12, 1, -36, [0, "d55mCOVE9KwIH+UUTKmQ4r"], [5, 27.8, 60.1]], [8, 0, -37, [0, "1dUm1geQ5MFqe7Db/PVCaP"], 4], -38], 4, 4, 4, 4, 1], [4, "4fxhnbxi5Ehonw8Yx1AHQH", null, null, null, 1, 0], [1, 609.9009999999998, 338.207, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [17, "car-3", 2, 33554432, [-45], [[[7, -40, [0, "7dE+nGj4hJc4YbWazFQPcN"], [5, 27.8, 60]], [15, true, 1, 0, -41, [0, "c5YEpJOG1PC4Xpu1p1t6bM"]], [12, 1, -42, [0, "e8ZoPFF69E9IeiE8+mMReN"], [5, 27.8, 60.1]], [8, 0, -43, [0, "efKbnyQFNMqprH4z/gWRpt"], 6], -44], 4, 4, 4, 4, 1], [4, "efvDRdqz5NPZaygIPCVbNV", null, null, null, 1, 0], [1, 610.325, -338.674, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [24, "car-4", 2, 33554432, [-51], [[[7, -46, [0, "37k3CkReZNv71HikERR8X5"], [5, 27.8, 60]], [15, true, 1, 0, -47, [0, "49IixABhlAvJ72mLDtGkdH"]], [12, 1, -48, [0, "c6nL4LB6ZLxLMNhG4ySw/1"], [5, 27.8, 60.1]], [8, 0, -49, [0, "ffj/37L4lFF56T2Z7asktU"], 8], -50], 4, 4, 4, 4, 1], [4, "58k4fC1TlFuY1UUHFw3KLL", null, null, null, 1, 0], [1, -610.157, -338.674, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [22, "cars", 33554432, 1, [2, 3, 4, 5], [[19, -52, [0, "55QaEEFuxE8qPGg1d77psJ"]]], [4, "54YmnXlspLyaBggn+ZgMJZ", null, null, null, 1, 0]], [6, 0, {}, 2, [9, "40tQNkNPtP4aURzO7VMMIR", null, null, -57, [10, "60gSiKdu9KG4eXfQWCmDFP", 1, [[16, "healthBar", ["_name"], -53], [2, ["_lpos"], -54, [1, -35, 0, 0]], [2, ["_lrot"], -55, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -56, [1, 0, 0, 90]], [5, 50, ["offsetY"], [1, ["88S4hp27BPFpL6l7nafPlR"]]], [3, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [5, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 1]], [6, 0, {}, 3, [9, "40tQNkNPtP4aURzO7VMMIR", null, null, -62, [10, "287242WK1AOLT+e6PfyRkJ", 1, [[16, "healthBar", ["_name"], -58], [2, ["_lpos"], -59, [1, 35, 0, 0]], [2, ["_lrot"], -60, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -61, [1, 0, 0, 90]], [3, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [5, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 3]], [6, 0, {}, 4, [9, "40tQNkNPtP4aURzO7VMMIR", null, null, -67, [10, "306+a8PTZIb7Cny3Fxe5lw", 1, [[16, "healthBar", ["_name"], -63], [2, ["_lpos"], -64, [1, 35, 0, 0]], [2, ["_lrot"], -65, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -66, [1, 0, 0, 90]], [3, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [5, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 5]], [6, 0, {}, 5, [9, "40tQNkNPtP4aURzO7VMMIR", null, null, -68, [10, "0agNocR6lCN7Zo/nfBHbBd", 1, [[5, "healthBar", ["_name"], [1, ["40tQNkNPtP4aURzO7VMMIR"]]], [3, ["_lpos"], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, -35, 0, 0]], [3, ["_lrot"], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [3, ["_euler"], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, 0, 0, 90]], [3, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [5, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 7]], [1, ["793wXpE0dMZZF5oHFdhgzy"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [6, 0, {}, 1, [9, "793wXpE0dMZZF5oHFdhgzy", null, null, -69, [10, "69TOUpT91HcYTh3TBFhXE5", 1, [[16, "PaintRoot", ["_name"], 11], [2, ["_lpos"], 11, [1, -639.97, -359.883, 0]], [2, ["_lrot"], 11, [3, 0, 0, 0, 1]], [2, ["_euler"], 11, [1, 0, 0, 0]]]], 0]], [23, "BulletRoot", 4, 1, [[19, -70, [0, "65jdRrHN1MXajZ1Iw7JXJg"]]], [4, "f7XLnOLRBDQ5oOUGi5ylgf", null, null, null, 1, 0]], [18, 10, 10, 50, 0.1, 1, 2, [0, "0e4GmtCgJFx4uJAqb2DWgy"]], [18, 10, 10, 50, 0.1, 1, 3, [0, "5eZuCQCkxFAJp5Gracyjcb"]], [18, 10, 10, 50, 0.1, 1, 4, [0, "aehScXeqtE35Vq3EsdwmPO"]], [26, 10, 10, 50, 0.1, 0.5, 2, 5, [0, "3cDyoIMhdGLpKxCGWxHODJ"]]], 0, [0, -1, 10, 0, -2, 9, 0, -3, 8, 0, -4, 7, 0, -5, 15, 0, 2, 7, 0, 2, 8, 0, 2, 9, 0, 2, 10, 0, 2, 7, 0, 6, 17, 0, 2, 8, 0, 6, 18, 0, 2, 9, 0, 6, 19, 0, 2, 10, 0, 6, 20, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 15, 0, -2, 16, 0, -3, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -5, 17, 0, -1, 7, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -5, 18, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -5, 19, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -5, 20, 0, -1, 10, 0, 0, 6, 0, 1, 12, 0, 1, 12, 0, 1, 12, 0, 1, 12, 0, 3, 7, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 3, 8, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 3, 9, 0, 3, 10, 0, 3, 15, 0, 0, 16, 0, 13, 1, 2, 7, 6, 3, 7, 6, 4, 7, 6, 5, 7, 6, 70], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20], [4, 4, 5, 4, 5, 4, 5, 4, 5, 5, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12], [6, 0, 5, 0, 5, 0, 5, 0, 7, 8, 1, 9, 2, 3, 4, 1, 10, 2, 3, 4, 1, 11, 2, 3, 4, 1, 12, 2, 3, 4]], [[{"name": "soccor_court", "rect": {"x": 0, "y": 0, "width": 1691, "height": 1266}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1691, "height": 1266}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-845.5, -633, 0, 845.5, -633, 0, -845.5, 633, 0, 845.5, 633, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1266, 1691, 1266, 0, 0, 1691, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -845.5, "y": -633, "z": 0}, "maxPos": {"x": 845.5, "y": 633, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [14], [13]]]]