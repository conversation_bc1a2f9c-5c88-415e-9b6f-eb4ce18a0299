[1, ["c7CfSpAIpItbqKNqaD7scb@f9941", "dcta33lHROwrrB/vxfhE6n@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "ffSxFV24tM2pvAbnQ+VxC8@f9941", "b5EDA9xoxOG4BzahbrTfn4@f9941", "9f2QDdIhtPiY8s+6NCQ8g1@f9941", "374ExlSsRBuIFpomb0Dp+o@f9941", "8eL0KXkaZHzIzXQ4qF541b@f9941", "24pwTaKGdEbY0aXpIMdeCd@f9941", "0adPQ1OoNEsa80qLFuph7J@6c48a", "57zIv2tepBbJICQmVTdBpN@f9941", "35fLH7RCdGd66+qjz5+d9V@f9941", "0adPQ1OoNEsa80qLFuph7J@f9941", "b7MFJ8MjNBwqr3fNq1j5dJ@f9941", "acTGImtcpAqo5l2VOrk8a0@f9941", "35fLH7RCdGd66+qjz5+d9V@6c48a", "374ExlSsRBuIFpomb0Dp+o@6c48a", "57zIv2tepBbJICQmVTdBpN@6c48a", "8eL0KXkaZHzIzXQ4qF541b@6c48a", "acTGImtcpAqo5l2VOrk8a0@6c48a", "b5EDA9xoxOG4BzahbrTfn4@6c48a", "b7MFJ8MjNBwqr3fNq1j5dJ@6c48a", "c7CfSpAIpItbqKNqaD7scb@6c48a", "dcta33lHROwrrB/vxfhE6n@6c48a", "ffSxFV24tM2pvAbnQ+VxC8@6c48a"], ["node", "_spriteFrame", "_parent", "_textureSource", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "_cameraComponent", "LevelSelectButton", "restartButton", "ai4RatioLabel", "ai3RatioLabel", "ai2RatioLabel", "ai1RatioLabel", "player<PERSON>ati<PERSON><PERSON><PERSON><PERSON>", "star3Sprite", "star2Sprite", "star1Sprite", "gameTimeLabel", "<PERSON><PERSON><PERSON><PERSON>", "performance<PERSON>abel", "titleLabel", "scene"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_active", "_id", "_components", "_parent", "_children", "_lpos", "_lrot", "_euler"], -1, 9, 1, 2, 5, 5, 5], ["cc.Node", ["_name", "_layer", "_components", "_lpos", "_parent", "_children", "_lscale", "_lrot", "_euler"], 1, 12, 5, 1, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_spriteFrame", "_color"], 1, 1, 6, 5], ["cc.Label", ["_string", "_actualFontSize", "_enableOutline", "_enableShadow", "_isBold", "_fontSize", "node", "_color"], -3, 1, 5], ["cc.Widget", ["_alignFlags", "_top", "_alignMode", "_bottom", "_right", "_left", "node"], -3, 1], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.Node", ["_name", "_id", "_layer", "_lpos", "_children", "_parent", "_components"], 0, 5, 9, 1, 2], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_far", "_visibility", "_fov", "_fovAxis", "_priority", "_clearFlags", "node", "_color"], -6, 1, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "_target", "_normalColor"], 2, 1, 1, 5], ["cc.SceneAsset", ["_name"], 2], ["85096jcoYBCMKDGuUlvynIB", ["node", "titleLabel", "performance<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "gameTimeLabel", "star1Sprite", "star2Sprite", "star3Sprite", "player<PERSON>ati<PERSON><PERSON><PERSON><PERSON>", "ai1RatioLabel", "ai2RatioLabel", "ai3RatioLabel", "ai4RatioLabel", "restartButton", "LevelSelectButton"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["cc.Scene", ["_name", "_children", "_globals"], 2, 2, 4], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingLeft", "_paddingRight", "_spacingX", "node"], -2, 1], ["daf21OCa1hMm660rOlgyMop", ["node"], 3, 1], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "_barSprite"], 1, 1, 1], ["8d994pxBTRN65Jo2JjZ4MAH", ["node", "countdown<PERSON><PERSON>l", "player<PERSON>ati<PERSON><PERSON><PERSON><PERSON>", "ai1RatioLabel", "ai2RatioLabel", "ai3RatioLabel", "ai4RatioLabel", "shootButton", "ammoLabel", "reloadProgressBar", "upButton", "downButton", "leftButton", "rightB<PERSON>on"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["b67f4UjjapGSoVG2Jvvuyl3", ["node", "playGround", "canvas", "spawnPoint", "camera", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enemyCountLabel", "pauseButton", "pausePanel", "gameOverPanel", "resumeButton", "mainMenuButton", "gameHUD"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["f2409Jfl1NKgaxzoqghBXWi", ["node"], 3, 1]], [[2, 0, 1, 4, 2, 3, 3], [6, 0, 1, 1], [6, 0, 1, 2, 1], [3, 1, 0, 2, 3, 3], [9, 0, 1, 3, 2, 2], [4, 0, 1, 4, 2, 3, 6, 7, 6], [1, 0, 5, 7, 8, 9, 2], [6, 0, 1], [1, 0, 1, 5, 6, 4, 7, 3], [4, 0, 1, 5, 2, 3, 6, 7, 6], [3, 0, 2, 3, 2], [2, 0, 1, 4, 2, 3, 7, 6, 8, 3], [3, 0, 2, 2], [1, 0, 1, 3, 6, 4, 7, 4], [1, 0, 1, 5, 4, 7, 3], [1, 0, 3, 5, 4, 3], [2, 0, 1, 4, 5, 2, 3, 3], [3, 1, 0, 2, 4, 3], [12, 0, 1, 1], [4, 0, 1, 5, 4, 2, 3, 6, 7, 7], [4, 0, 1, 4, 2, 6, 7, 5], [25, 0, 1, 2, 3, 3], [10, 0, 2], [1, 0, 2, 1, 6, 4, 4], [1, 0, 1, 5, 6, 4, 3], [1, 0, 2, 1, 5, 6, 4, 4], [1, 0, 1, 5, 4, 3], [2, 0, 1, 5, 2, 3], [2, 0, 1, 5, 2, 3, 3], [2, 0, 1, 4, 2, 3, 6, 3], [7, 0, 1, 4, 3, 3], [7, 0, 2, 5, 6, 3, 3], [3, 1, 0, 2, 4, 3, 3], [3, 0, 2, 4, 3, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1], [5, 0, 2, 6, 3], [5, 0, 1, 3, 2, 6, 5], [5, 0, 4, 1, 6, 4], [5, 0, 5, 1, 6, 4], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5, 6, 7, 1], [15, 0, 1, 1], [16, 0, 1, 1], [17, 1], [18, 1], [19, 1], [20, 0, 2], [21, 1], [22, 1], [23, 0, 1, 2, 3, 4, 5, 6], [24, 0, 1], [8, 0, 5, 6, 1, 2, 3, 4, 9, 10, 8], [8, 0, 7, 1, 2, 3, 8, 4, 9, 10, 8], [9, 0, 1, 2, 2], [4, 0, 1, 2, 3, 6, 7, 5], [26, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1], [27, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [28, 0, 1]], [[[{"name": "panel2", "rect": {"x": 0, "y": 0, "width": 928, "height": 838}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 928, "height": 838}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-464, -419, 0, 464, -419, 0, -464, 419, 0, 464, 419, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 838, 928, 838, 0, 0, 928, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -464, "y": -419, "z": 0}, "maxPos": {"x": 464, "y": 419, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [11]], [[[22, "gamescene"], [23, "gameOverPanel", false, 524288, [-18, -19, -20, -21, -22, -23, -24, -25], [[1, -1, [5, 697.2560000000001, 602.98]], [10, 0, -2, 15], [34, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]]], [27, "GameHUD", 524288, [-28, -29, -30, -31, -32, -33, -34], [[[1, -26, [5, 1280, 720]], -27], 4, 1]], [13, "UICanvas", 524288, "d175nThgdJ/In481e1Y5Bq", [-39, -40, 2, -41, 1], [[1, -35, [5, 1280, 720]], [18, -37, -36], [35, 45, 1, -38]], [1, 640, 360, 0]], [30, "spwanpoints", "87GGE01slMnLBedPHLZh6l", [[6, "point1", -42, [1, 1250, 1053.802, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [6, "point2", -43, [1, 1250, 1412.514, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [6, "point3", -44, [1, 1250, 686.162, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [6, "point4", -45, [1, 2200, 691.991, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [6, "point5", -46, [1, 2200, 1053.803, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [6, "point6", -47, [1, 2200, 1416.186, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]]], [1, -1042.636, -690.422, 0]], [13, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-52, -53], [[1, -48, [5, 1280, 720]], [18, -50, -49], [36, 45, 5.684341886080802e-14, 5.684341886080802e-14, 1, -51]], [1, 640, 360.00000000000006, 0]], [24, "buttons", 524288, 2, [-55, -56, -57, -58, -59], [[7, -54]]], [8, "<PERSON><PERSON><PERSON>", 524288, 1, [-61, -62, -63, -64, -65], [[7, -60]], [1, -87.418, 12.883, 0]], [39, "gamescene", [5, 3, 4, -66, -67], [40, [41, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [42, [4, 4283190348], [0, 512, 512]], [43], [44], [45], [46, false], [47], [48]]], [0, "pauseButton", 524288, 3, [[[1, -68, [5, 60, 60]], [3, 1, 0, -69, 0], -70, [37, 33, 26.44899999999995, 11.946999999999974, -71]], 4, 4, 1, 4], [1, 583.551, 318.053, 0]], [28, "PlayerHp", 524288, [-76], [[[1, -72, [5, 300, 15]], [3, 1, 0, -73, 1], -74, [38, 9, 4.9325000000000045, -53.615, -75]], 4, 4, 1, 4], [1, 122.367, 70.312, 0]], [8, "<PERSON><PERSON><PERSON>", 524288, 2, [-78, -79, -80, -81], [[2, -77, [5, 100, 49.981], [0, 0, 0.5]]], [1, -608.344, 211.042, 0]], [25, "PausePanel", false, 524288, 3, [-84, -85], [[1, -82, [5, 736.704, 519.579]], [10, 0, -83, 11]]], [16, "reviewLable", 524288, 1, [-88, -89, -90], [[[2, -86, [5, 164, 54.4], [0, 0, 0.5]], -87], 4, 1], [1, -248.751, 153.187, 0]], [0, "shoot", 524288, 6, [[[1, -91, [5, 120, 110]], [10, 0, -92, 2], -93], 4, 4, 1], [1, -444.039, -61.346, 0]], [29, "up", 524288, 6, [[[7, -94], [3, 1, 0, -95, 3], -96], 4, 4, 1], [1, 445, -141.3, 0], [1, 1.2, 1.2, 1]], [11, "down", 524288, 6, [[[7, -97], [3, 1, 0, -98, 4], -99], 4, 4, 1], [1, 445, -277.97, 0], [3, 0, 0, 1, 6.123233995736766e-17], [1, 1.2, 1.2, 1], [1, 180, 180, 7.016709298534876e-15]], [11, "left", 524288, 6, [[[1, -100, [5, 120, 120]], [3, 1, 0, -101, 5], -102], 4, 4, 1], [1, -534.14, -207.422, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, 90]], [11, "right", 524288, 6, [[[1, -103, [5, 120, 120]], [3, 1, 0, -104, 6], -105], 4, 4, 1], [1, -347.03, -207.422, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [16, "reload", 524288, 2, [-109], [[[1, -106, [5, 60.42099999999999, 5.605]], [32, 1, 0, -107, [4, 4289583323], 7], -108], 4, 4, 1], [1, -448.353, -5.115, 0]], [8, "Layout", 524288, 12, [-112, -113], [[1, -110, [5, 290, 100]], [49, 1, 1, 20, 20, 20, -111]], [1, 0, -40.852, 0]], [0, "resume", 524288, 20, [[[1, -114, [5, 120, 120]], [3, 1, 0, -115, 9], -116], 4, 4, 1], [1, -65, -13.417, 0]], [0, "backtomenu", 524288, 20, [[[1, -117, [5, 110, 110]], [3, 1, 0, -118, 10], -119], 4, 4, 1], [1, 70, -13.417, 0]], [0, "backtomenu", 524288, 1, [[[7, -120], [3, 1, 0, -121, 13], -122], 4, 4, 1], [1, 247.778, -211.065, 0]], [0, "restart", 524288, 1, [[[1, -123, [5, 70, 70]], [3, 1, 0, -124, 14], -125], 4, 4, 1], [1, 139.084, -212.43, 0]], [0, "Camera", 33554432, 5, [[-126, [50, -127]], 1, 4], [1, 0, 0, 1000]], [8, "playerinfos", 524288, 2, [10, -129], [[1, -128, [5, 65.131, 48.394]]], [1, -580, 253.588, 0]], [26, "PlayGround", 33554432, 5, [[1, -130, [5, 1, 1]]]], [0, "countdown", 524288, 2, [[[1, -131, [5, 70.73828125, 54.4]], -132], 4, 1], [1, 6.219, 318.008, 0]], [0, "Bar", 524288, 10, [[[2, -133, [5, 300, 15], [0, 0, 0.5]], -134], 4, 1], [1, -150, 0, 0]], [8, "paint", 524288, 26, [-136], [[7, -135]], [1, -28.799, 0, 0]], [0, "<PERSON><PERSON><PERSON>", 524288, 30, [[[2, -137, [5, 154.0439453125, 54.4], [0, 0, 0.5]], -138], 4, 1], [1, -1.516, -9.959, 0]], [0, "opponentCount", 524288, 2, [[[2, -139, [5, 170.6845703125, 54.4], [0, 0, 0.5]], -140], 4, 1], [1, -610.706, 287.038, 0]], [0, "ai-1", 524288, 11, [[[2, -141, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -142], 4, 1], [1, 0, -15.057, 0]], [0, "ai-2", 524288, 11, [[[2, -143, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -144], 4, 1], [1, 0, -49.108, 0]], [0, "ai-3", 524288, 11, [[[2, -145, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -146], 4, 1], [1, 0, -83.952, 0]], [0, "ai-4", 524288, 11, [[[2, -147, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -148], 4, 1], [1, 0, -113.178, 0]], [0, "ammo", 524288, 2, [[[1, -149, [5, 62.90625, 54.4]], -150], 4, 1], [1, -447.105, 14.594, 0]], [0, "Bar", 524288, 19, [[[2, -151, [5, 60.421, 5.122], [0, 0, 0.5]], -152], 4, 1], [1, -29.613999999999997, -0.15900000000000003, 0]], [14, "pause", 524288, 12, [[1, -153, [5, 262.051, 124.21035172363621]], [10, 0, -154, 8]], [1, 0, 117.101, 0]], [14, "mask", 524288, 1, [[1, -155, [5, 652.0539999999999, 488.35599999999994]], [33, 0, -156, [4, 1555543993], 12]], [1, 2.273999999999997, -32.86250000000003, 0]], [0, "GameOverLable", 524288, 1, [[[1, -157, [5, 164, 54.4]], -158], 4, 1], [1, -0.866, 256.191, 0]], [0, "TimeLable", 524288, 1, [[[2, -159, [5, 84, 54.4], [0, 0, 0.5]], -160], 4, 1], [1, -246.196, -116.715, 0]], [0, "star1", 524288, 13, [[[1, -161, [5, 60, 60]], -162], 4, 1], [1, 372.454, -1.203, 0]], [0, "star2", 524288, 13, [[[1, -163, [5, 60, 60]], -164], 4, 1], [1, 413.89, -1.203, 0]], [0, "star3", 524288, 13, [[[1, -165, [5, 60, 60]], -166], 4, 1], [1, 457.51, -1.203, 0]], [0, "player", 524288, 7, [[[2, -167, [5, 215.23046875, 54.4], [0, 0, 0.5]], -168], 4, 1], [1, -158.526, 57.495, 0]], [0, "AI-1", 524288, 7, [[[2, -169, [5, 159.625, 54.4], [0, 0, 0.5]], -170], 4, 1], [1, -158.495, 0.92, 0]], [0, "AI-2", 524288, 7, [[[2, -171, [5, 159.625, 54.4], [0, 0, 0.5]], -172], 4, 1], [1, 122.996, 1.886, 0]], [0, "AI-3", 524288, 7, [[[2, -173, [5, 159.625, 54.4], [0, 0, 0.5]], -174], 4, 1], [1, -163.2, -59.264, 0]], [0, "AI-4", 524288, 7, [[[2, -175, [5, 159.625, 54.4], [0, 0, 0.5]], -176], 4, 1], [1, 121.1, -54.495, 0]], [0, "rewardLable", 524288, 1, [[[2, -177, [5, 164, 54.4], [0, 0, 0.5]], -178], 4, 1], [1, 35.027, -116.404, 0]], [51, 0, 25.363636363636363, 1, 381.4866760168303, 0, 2000, 1108344836, 25, [4, 16777215]], [31, "Camera", 33554432, 3, [-179], [1, 0, 0, 1000]], [52, 0, 1, 381.4866760168303, 0, 2000, 6, 524288, 53, [4, 4278190080]], [4, 3, 9, [4, 4292269782], 9], [5, "120", 40, true, true, true, 28, [4, 4278255380]], [17, 1, 0, 29, [4, 4278255462]], [21, 300, 1, 10, 57], [9, "玩家：10%", 30, 30, true, true, 31, [4, 4284177395]], [19, "剩余对手：4", 30, 30, true, true, true, 32, [4, 4284045791]], [9, "ai-1", 30, 30, true, true, 33, [4, 4278224383]], [9, "ai-2", 30, 30, true, true, 34, [4, 4278226685]], [9, "ai-3", 30, 30, true, true, 35, [4, 4278221567]], [9, "ai-4", 30, 30, true, true, 36, [4, 4281174768]], [53, 3, 14, 14], [4, 3, 15, [4, 4292269782], 15], [4, 3, 16, [4, 4292269782], 16], [4, 3, 17, [4, 4292269782], 17], [4, 3, 18, [4, 4292269782], 18], [19, "ammo", 20, 20, true, true, true, 37, [4, 4285393139]], [17, 1, 0, 38, [4, 4278253035]], [21, 60.421, 1, 19, 71], [55, 2, 56, 59, 61, 62, 63, 64, 65, 70, 72, 66, 67, 68, 69], [4, 3, 21, [4, 4292269782], 21], [4, 3, 22, [4, 4292269782], 22], [20, "游戏结束", 40, true, true, 41, [4, 4292607817]], [20, "时间", 40, true, true, 42, [4, 4292607817]], [12, 0, 43], [12, 0, 44], [12, 0, 45], [5, "表现评价", 40, true, true, true, 13, [4, 4290080757]], [5, "player:20%", 40, true, true, true, 46, [4, 4286019573]], [5, "AI1:20%", 40, true, true, true, 47, [4, 4281786343]], [5, "AI2:20%", 40, true, true, true, 48, [4, 4286572139]], [5, "AI3:20%", 40, true, true, true, 49, [4, 4292194730]], [5, "AI4:20%", 40, true, true, true, 50, [4, 4290816725]], [54, "获得奖励", 40, true, true, 51, [4, 4292398852]], [4, 3, 23, [4, 4292269782], 23], [4, 3, 24, [4, 4292269782], 24], [15, "GameManager", "dcbqoNkjtBg5W8+uTDZHUD", 8, [[56, -180, 27, 5, 4, 25, 58, 60, 55, 12, 1, 74, 75, 73]]], [15, "AIController", "9d9+jzMZ1Fa5Gun1m2L8KH", 8, [[57, -181]]]], 0, [0, 0, 1, 0, 0, 1, 0, 9, 88, 0, 10, 89, 0, 11, 86, 0, 12, 85, 0, 13, 84, 0, 14, 83, 0, 15, 82, 0, 16, 80, 0, 17, 79, 0, 18, 78, 0, 19, 77, 0, 20, 87, 0, 21, 81, 0, 22, 76, 0, 0, 1, 0, -1, 40, 0, -2, 41, 0, -3, 42, 0, -4, 13, 0, -5, 7, 0, -6, 51, 0, -7, 23, 0, -8, 24, 0, 0, 2, 0, -2, 73, 0, -1, 28, 0, -2, 26, 0, -3, 32, 0, -4, 11, 0, -5, 6, 0, -6, 37, 0, -7, 19, 0, 0, 3, 0, 8, 54, 0, 0, 3, 0, 0, 3, 0, -1, 53, 0, -2, 9, 0, -4, 12, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 0, 5, 0, 8, 52, 0, 0, 5, 0, 0, 5, 0, -1, 25, 0, -2, 27, 0, 0, 6, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, -5, 18, 0, 0, 7, 0, -1, 46, 0, -2, 47, 0, -3, 48, 0, -4, 49, 0, -5, 50, 0, -4, 90, 0, -5, 91, 0, 0, 9, 0, 0, 9, 0, -3, 55, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -3, 58, 0, 0, 10, 0, -1, 29, 0, 0, 11, 0, -1, 33, 0, -2, 34, 0, -3, 35, 0, -4, 36, 0, 0, 12, 0, 0, 12, 0, -1, 39, 0, -2, 20, 0, 0, 13, 0, -2, 81, 0, -1, 43, 0, -2, 44, 0, -3, 45, 0, 0, 14, 0, 0, 14, 0, -3, 65, 0, 0, 15, 0, 0, 15, 0, -3, 66, 0, 0, 16, 0, 0, 16, 0, -3, 67, 0, 0, 17, 0, 0, 17, 0, -3, 68, 0, 0, 18, 0, 0, 18, 0, -3, 69, 0, 0, 19, 0, 0, 19, 0, -3, 72, 0, -1, 38, 0, 0, 20, 0, 0, 20, 0, -1, 21, 0, -2, 22, 0, 0, 21, 0, 0, 21, 0, -3, 74, 0, 0, 22, 0, 0, 22, 0, -3, 75, 0, 0, 23, 0, 0, 23, 0, -3, 88, 0, 0, 24, 0, 0, 24, 0, -3, 89, 0, -1, 52, 0, 0, 25, 0, 0, 26, 0, -2, 30, 0, 0, 27, 0, 0, 28, 0, -2, 56, 0, 0, 29, 0, -2, 57, 0, 0, 30, 0, -1, 31, 0, 0, 31, 0, -2, 59, 0, 0, 32, 0, -2, 60, 0, 0, 33, 0, -2, 61, 0, 0, 34, 0, -2, 62, 0, 0, 35, 0, -2, 63, 0, 0, 36, 0, -2, 64, 0, 0, 37, 0, -2, 70, 0, 0, 38, 0, -2, 71, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, -2, 76, 0, 0, 42, 0, -2, 77, 0, 0, 43, 0, -2, 78, 0, 0, 44, 0, -2, 79, 0, 0, 45, 0, -2, 80, 0, 0, 46, 0, -2, 82, 0, 0, 47, 0, -2, 83, 0, 0, 48, 0, -2, 84, 0, 0, 49, 0, -2, 85, 0, 0, 50, 0, -2, 86, 0, 0, 51, 0, -2, 87, 0, -1, 54, 0, 0, 90, 0, 0, 91, 0, 23, 8, 1, 2, 3, 2, 2, 3, 3, 2, 8, 4, 2, 8, 5, 2, 8, 10, 2, 26, 181], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 55, 55, 55, 57, 71, 74, 74, 74, 74, 75, 75, 75, 75, 78, 79, 80, 88, 88, 88, 88, 89], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 5, 6, 7, 1, 1, 4, 5, 6, 7, 4, 5, 6, 7, 1, 1, 1, 4, 5, 6, 7, 4], [6, 7, 12, 0, 0, 0, 0, 7, 13, 8, 1, 14, 15, 1, 9, 16, 6, 2, 3, 4, 10, 10, 8, 2, 3, 4, 1, 2, 3, 4, 5, 5, 5, 1, 2, 3, 4, 9]], [[{"name": "pause", "rect": {"x": 0, "y": 0, "width": 1440, "height": 946}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1440, "height": 946}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-720, -473, 0, 720, -473, 0, -720, 473, 0, 720, 473, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 946, 1440, 946, 0, 0, 1440, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -720, "y": -473, "z": 0}, "maxPos": {"x": 720, "y": 473, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [17]], [[{"name": "resume", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [18]], [[{"name": "fire", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [19]], [[{"name": "restart", "rect": {"x": 0, "y": 0, "width": 908, "height": 929}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 908, "height": 929}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-454, -464.5, 0, 454, -464.5, 0, -454, 464.5, 0, 454, 464.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 929, 908, 929, 0, 0, 908, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -454, "y": -464.5, "z": 0}, "maxPos": {"x": 454, "y": 464.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [20]], [[{"name": "gameover", "rect": {"x": 4, "y": 0, "width": 1331, "height": 1433}, "offset": {"x": -2.5, "y": 0}, "originalSize": {"width": 1344, "height": 1433}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-665.5, -716.5, 0, 665.5, -716.5, 0, -665.5, 716.5, 0, 665.5, 716.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [4, 1433, 1335, 1433, 4, 0, 1335, 0], "nuv": [0.002976190476190476, 0, 0.9933035714285714, 0, 0.002976190476190476, 1, 0.9933035714285714, 1], "minPos": {"x": -665.5, "y": -716.5, "z": 0}, "maxPos": {"x": 665.5, "y": 716.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [21]], [[{"name": "pause_bt", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [22]], [[{"name": "default_panel", "rect": {"x": 0, "y": 0, "width": 20, "height": 20}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 20}, "rotated": false, "capInsets": [8, 8, 8, 8], "vertices": {"rawPosition": [-10, -10, 0, 10, -10, 0, -10, 10, 0, 10, 10, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 20, 20, 20, 0, 0, 20, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -10, "y": -10, "z": 0}, "maxPos": {"x": 10, "y": 10, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [23]], [[{"name": "dir", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [24]], [[{"name": "backtoselect", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [25]], [[{"name": "star", "rect": {"x": 0, "y": 0, "width": 200, "height": 200}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 200, "height": 200}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-100, -100, 0, 100, -100, 0, -100, 100, 0, 100, 100, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 200, 200, 200, 0, 0, 200, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -100, "y": -100, "z": 0}, "maxPos": {"x": 100, "y": 100, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [3], [26]]]]